using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WorkOrder.Domain
{
    /// <summary>
    /// 问诊单实体类
    /// </summary>
    [Table("ConsultationOrder")]
    public class ConsultationOrder
    {
        /// <summary>
        /// 问诊订单编号（主键）- 格式：CON + 年月日 + 6位序号，如：CON20240101000001
        /// </summary>
        [Key]
        public string OrderNumber { get; set; }

        /// <summary>
        /// 订单类型（视频问诊、电话问诊、图文问诊等）
        /// </summary>


        public string OrderType { get; set; }

        /// <summary>
        /// 问诊方式（视频问诊、电话问诊、图文问诊、极速电话、复诊开药等）
        /// </summary>


        public string ConsultationMethod { get; set; }

        /// <summary>
        /// 问诊来源（视频问诊、电话问诊、极速图文等）
        /// </summary>

        public string ConsultationSource { get; set; }

        /// <summary>
        /// 订单状态（待支付、进行中、已完成、已取消、已退诊）
        /// </summary>

        public string OrderStatus { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>

        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>

        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>

        public decimal ActualPayment { get; set; }

        /// <summary>
        /// 问诊费用
        /// </summary>

        public decimal ConsultationFee { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 提交问诊时间
        /// </summary>

        public DateTime SubmitTime { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; }

        /// <summary>
        /// 患者性别
        /// </summary>
        public string PatientGender { get; set; }

        /// <summary>
        /// 患者年龄
        /// </summary>

        public int PatientAge { get; set; }

        /// <summary>
        /// 患者手机号
        /// </summary>
        public string PatientPhone { get; set; }

        /// <summary>
        /// 病情描述
        /// </summary>
        public string SymptomDescription { get; set; }

        /// <summary>
        /// 就诊情况
        /// </summary>
        public string? MedicalHistory { get; set; }

        /// <summary>
        /// 预约时间
        /// </summary>
        public DateTime? AppointmentTime { get; set; }

        /// <summary>
        /// 提交时间（用于显示）
        /// </summary>
        public DateTime? SubmissionTime { get; set; }

        /// <summary>
        /// 医生姓名
        /// </summary>
        public string DoctorName { get; set; }

        /// <summary>
        /// 科室
        /// </summary>
        public string Department { get; set; }

        /// <summary>
        /// 接诊时间
        /// </summary>
        public DateTime? ConsultationStartTime { get; set; }

        /// <summary>
        /// 通话时长（分钟）
        /// </summary>
        public int? CallDurationMinutes { get; set; }

        /// <summary>
        /// 患者评分（1-5星）
        /// </summary>
        public int? PatientRating { get; set; }

        /// <summary>
        /// 患者评价
        /// </summary>
        public string? PatientReview { get; set; }

        /// <summary>
        /// 问诊结束时间
        /// </summary>
        public DateTime? ConsultationEndTime { get; set; }

        /// <summary>
        /// 是否有问诊记录
        /// </summary>
        public bool HasConsultationRecord { get; set; } = false;

        /// <summary>
        /// 用户名（提交订单的用户）
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 是否已删除（软删除标记）
        /// </summary>
        public bool IsDelete { get; set; } = false;
    }
}
