{"Version": 1, "WorkspaceRootPath": "D:\\实训一\\项目\\工单\\WorkOrder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.write\\mappingprofiles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|solutionrelative:workorder.api.write\\mappingprofiles.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\handler\\getconsultationorderdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\handler\\getconsultationorderdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.write\\application\\handler\\consultationorderhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|solutionrelative:workorder.api.write\\application\\handler\\consultationorderhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.write\\application\\command\\consultationordercommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|solutionrelative:workorder.api.write\\application\\command\\consultationordercommands.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\handler\\consultationorderhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\handler\\consultationorderhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\consultationorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\consultationorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\controllers\\managementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\controllers\\managementcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\command\\consultationorderquerycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\command\\consultationorderquerycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\command\\registrationorderquerycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\command\\registrationorderquerycommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.write\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{E7B583CE-9111-456A-A066-668D73FC7067}|WorkOrder.API.Write\\WorkOrder.API.Write.csproj|solutionrelative:workorder.api.write\\servicecollectionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\handler\\registrationorderhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\handler\\registrationorderhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\command\\getorderdetailcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\command\\getorderdetailcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\command\\getconsultationorderdetailcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\command\\getconsultationorderdetailcommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.api.read\\application\\handler\\getorderdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F201CF2E-00DF-4C86-9345-A122784C3890}|WorkOrder.API.Read\\WorkOrder.API.Read.csproj|solutionrelative:workorder.api.read\\application\\handler\\getorderdetailhandler.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\prescriptionorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\prescriptionorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\medicineorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\medicineorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\registrationorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\registrationorder.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\refundapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\refundapplication.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\permissionmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\permissionmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\rolemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\rolemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\rolepermissionmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\rolepermissionmodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\usermodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\usermodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|d:\\实训一\\项目\\工单\\workorder\\workorder.domain\\userrolemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{1A83119D-3D53-402D-B273-364021977616}|WorkOrder.Domain\\WorkOrder.Domain.csproj|solutionrelative:workorder.domain\\userrolemodel.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "MappingProfiles.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\MappingProfiles.cs", "RelativeDocumentMoniker": "WorkOrder.API.Write\\MappingProfiles.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\MappingProfiles.cs", "RelativeToolTip": "WorkOrder.API.Write\\MappingProfiles.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAsAAABTAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T12:36:17.936Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ConsultationOrderHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\Application\\Handler\\ConsultationOrderHandler.cs", "RelativeDocumentMoniker": "WorkOrder.API.Write\\Application\\Handler\\ConsultationOrderHandler.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\Application\\Handler\\ConsultationOrderHandler.cs", "RelativeToolTip": "WorkOrder.API.Write\\Application\\Handler\\ConsultationOrderHandler.cs", "ViewState": "AQIAAHAAAAAAAAAAAAAUwIMAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T12:27:12.186Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "ConsultationOrderCommands.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\Application\\command\\ConsultationOrderCommands.cs", "RelativeDocumentMoniker": "WorkOrder.API.Write\\Application\\command\\ConsultationOrderCommands.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\Application\\command\\ConsultationOrderCommands.cs", "RelativeToolTip": "WorkOrder.API.Write\\Application\\command\\ConsultationOrderCommands.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABwAAAAGAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T12:26:50.75Z", "EditorCaption": ""}, {"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 4, "Title": "ConsultationOrderHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\ConsultationOrderHandler.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\Handler\\ConsultationOrderHandler.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\ConsultationOrderHandler.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\Handler\\ConsultationOrderHandler.cs", "ViewState": "AQIAABUAAAAAAAAAAAAowBoAAABMAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T03:29:07.003Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "ConsultationOrderQueryCommand.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\ConsultationOrderQueryCommand.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\command\\ConsultationOrderQueryCommand.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\ConsultationOrderQueryCommand.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\command\\ConsultationOrderQueryCommand.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA4AAAAsAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T03:28:58.056Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ConsultationOrder.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\ConsultationOrder.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\ConsultationOrder.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\ConsultationOrder.cs", "RelativeToolTip": "WorkOrder.Domain\\ConsultationOrder.cs", "ViewState": "AQIAAKAAAAAAAAAAAAAuwLYAAAAcAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:56:16.767Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "GetConsultationOrderDetailHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\GetConsultationOrderDetailHandler.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\Handler\\GetConsultationOrderDetailHandler.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\GetConsultationOrderDetailHandler.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\Handler\\GetConsultationOrderDetailHandler.cs", "ViewState": "AQIAAAwAAAAAAAAAAAAMwBsAAABhAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T06:29:12.403Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "RegistrationOrderQuerycommand.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\RegistrationOrderQuerycommand.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\command\\RegistrationOrderQuerycommand.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\RegistrationOrderQuerycommand.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\command\\RegistrationOrderQuerycommand.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA8AAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T05:47:10.712Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "ManagementController.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Controllers\\ManagementController.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Controllers\\ManagementController.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Controllers\\ManagementController.cs", "RelativeToolTip": "WorkOrder.API.Read\\Controllers\\ManagementController.cs", "ViewState": "AQIAAAsAAAAAAAAAAAAzwDcAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:42:16.768Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "ServiceCollectionExtensions.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\ServiceCollectionExtensions.cs", "RelativeDocumentMoniker": "WorkOrder.API.Write\\ServiceCollectionExtensions.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Write\\ServiceCollectionExtensions.cs", "RelativeToolTip": "WorkOrder.API.Write\\ServiceCollectionExtensions.cs", "ViewState": "AQIAABgAAAAAAAAAAAAgwCUAAAAaAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T11:54:04.42Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "RegistrationOrderHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\RegistrationOrderHandler.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\Handler\\RegistrationOrderHandler.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\RegistrationOrderHandler.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\Handler\\RegistrationOrderHandler.cs", "ViewState": "AQIAAAoAAAAAAAAAAAAQwBkAAAAhAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T07:20:02.557Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "GetOrderDetailCommand.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\GetOrderDetailCommand.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\command\\GetOrderDetailCommand.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\GetOrderDetailCommand.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\command\\GetOrderDetailCommand.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAcAAAAMAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T03:26:58.711Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "GetConsultationOrderDetailCommand.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\GetConsultationOrderDetailCommand.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\command\\GetConsultationOrderDetailCommand.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\command\\GetConsultationOrderDetailCommand.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\command\\GetConsultationOrderDetailCommand.cs", "ViewState": "AQIAALsAAAAAAAAAAAA6wAAAAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T06:28:38.397Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "GetOrderDetailHandler.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\GetOrderDetailHandler.cs", "RelativeDocumentMoniker": "WorkOrder.API.Read\\Application\\Handler\\GetOrderDetailHandler.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.API.Read\\Application\\Handler\\GetOrderDetailHandler.cs", "RelativeToolTip": "WorkOrder.API.Read\\Application\\Handler\\GetOrderDetailHandler.cs", "ViewState": "AQIAAC0AAAAAAAAAAAAowC8AAAAvAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T01:15:06.988Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PrescriptionOrder.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\PrescriptionOrder.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\PrescriptionOrder.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\PrescriptionOrder.cs", "RelativeToolTip": "WorkOrder.Domain\\PrescriptionOrder.cs", "ViewState": "AQIAALkAAAAAAAAAAAAYwLwAAAAzAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:55:18.548Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "MedicineOrder.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\MedicineOrder.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\MedicineOrder.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\MedicineOrder.cs", "RelativeToolTip": "WorkOrder.Domain\\MedicineOrder.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA8AAAA/AAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:49:56.272Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "UserModel.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\UserModel.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\UserModel.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\UserModel.cs", "RelativeToolTip": "WorkOrder.Domain\\UserModel.cs", "ViewState": "AQIAAAMAAAAAAAAAAAAAABkAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:50:50.207Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "RegistrationOrder.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RegistrationOrder.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\RegistrationOrder.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RegistrationOrder.cs", "RelativeToolTip": "WorkOrder.Domain\\RegistrationOrder.cs", "ViewState": "AQIAAGwAAAAAAAAAAAAAAEgAAAAUAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:53:49.661Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "RoleModel.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RoleModel.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\RoleModel.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RoleModel.cs", "RelativeToolTip": "WorkOrder.Domain\\RoleModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAA4AAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:51:20.266Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "PermissionModel.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\PermissionModel.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\PermissionModel.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\PermissionModel.cs", "RelativeToolTip": "WorkOrder.Domain\\PermissionModel.cs", "ViewState": "AQIAAAYAAAAAAAAAAAAAAA4AAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:51:27.056Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "RefundApplication.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RefundApplication.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\RefundApplication.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RefundApplication.cs", "RelativeToolTip": "WorkOrder.Domain\\RefundApplication.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAkAAAAiAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:49:01.775Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "RolePermissionModel.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RolePermissionModel.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\RolePermissionModel.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\RolePermissionModel.cs", "RelativeToolTip": "WorkOrder.Domain\\RolePermissionModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABMAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:53:35.029Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "UserRoleModel.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\UserRoleModel.cs", "RelativeDocumentMoniker": "WorkOrder.Domain\\UserRoleModel.cs", "ToolTip": "D:\\实训一\\项目\\工单\\WorkOrder\\WorkOrder.Domain\\UserRoleModel.cs", "RelativeToolTip": "WorkOrder.Domain\\UserRoleModel.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAABMAAAAWAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T12:53:20.749Z"}]}]}]}