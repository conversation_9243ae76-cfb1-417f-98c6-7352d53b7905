{"format": 1, "restore": {"D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\DeepSeek.csproj": {}}, "projects": {"D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\DeepSeek.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\DeepSeek.csproj", "projectName": "DeepSeek", "projectPath": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\DeepSeek.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Http": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[9.0.7, )"}, "Microsoft.SemanticKernel": {"target": "Package", "version": "[1.61.0, )"}, "OllamaSharp": {"target": "Package", "version": "[5.3.3, )"}, "System.Text.Json": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}}}