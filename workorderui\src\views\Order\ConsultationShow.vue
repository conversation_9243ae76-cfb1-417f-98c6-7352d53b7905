<template>
    <div class="consultation-management">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>问诊单管理</h1>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-header">
                <el-icon>
                    <Grid />
                </el-icon>
                <span>筛选</span>
            </div>
            <div class="filter-content">
                <div class="filter-row">

                    <el-input v-model="filterForm.patientName" placeholder="患者姓名"
                        style="width: 150px; margin-right: 15px;" clearable />
                    <el-select v-model="filterForm.orderStatus" placeholder="订单状态"
                        style="width: 120px; margin-right: 15px;" clearable>
                        <el-option label="待支付" value="待支付" />
                        <el-option label="进行中" value="进行中" />
                        <el-option label="已完成" value="已完成" />
                        <el-option label="已取消" value="已取消" />
                        <el-option label="已退诊" value="已退诊" />
                    </el-select>
                    <el-select v-model="filterForm.consultationMethod" placeholder="问诊方式"
                        style="width: 150px; margin-right: 15px;" clearable>
                        <el-option label="视频问诊" value="视频问诊" />
                        <el-option label="电话问诊" value="电话问诊" />
                        <el-option label="图文问诊" value="图文问诊" />
                        <el-option label="极速电话" value="极速电话" />
                        <el-option label="复诊开药" value="复诊开药" />
                    </el-select>
                    <span class="date-label">时间范围：</span>
                    <el-date-picker v-model="filterForm.beginTime" type="datetime" placeholder="开始时间"
                        style="width: 180px; margin-right: 10px;" format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss" />
                    <el-date-picker v-model="filterForm.endTime" type="datetime" placeholder="结束时间"
                        style="width: 180px; margin-right: 15px;" format="YYYY-MM-DD HH:mm:ss"
                        value-format="YYYY-MM-DD HH:mm:ss" />
                    <el-button type="primary" @click="handleFilter">筛选</el-button>
                    <el-button @click="handleReset">重置</el-button>
                </div>
            </div>
        </div>

        <!-- 数据列表区域 -->
        <div class="data-section">
            <div class="data-header">
                <el-icon>
                    <Grid />
                </el-icon>
                <span>数据列表</span>
            </div>

            <!-- 状态标签页 -->
            <div class="status-tabs">
                <el-tabs v-model="activeStatus">
                    <el-tab-pane :label="`全部 (${getStatusCount('all')})`" name="all" />
                    <el-tab-pane :label="`待支付 (${getStatusCount('待支付')})`" name="待支付" />
                    <el-tab-pane :label="`已完成 (${getStatusCount('已完成')})`" name="已完成" />
                    <el-tab-pane :label="`已取消 (${getStatusCount('已取消')})`" name="已取消" />
                    <el-tab-pane :label="`进行中 (${getStatusCount('进行中')})`" name="进行中" />
                    <el-tab-pane :label="`已退诊 (${getStatusCount('已退诊')})`" name="已退诊" />
                </el-tabs>
            </div>

            <!-- 数据表格 -->
            <el-table :data="orderList" style="width: 100%" v-loading="loading">
                <el-table-column prop="orderNumber" label="订单编号" width="180" />
                <el-table-column prop="submitTime" label="提交时间" width="180">
                    <template #default="scope">
                        {{ formatDateTime(scope.row.submitTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="patientName" label="患者姓名" width="120" />
                <el-table-column prop="doctorName" label="医生" width="120" />
                <el-table-column prop="consultationMethod" label="问诊方式" width="120" />
                <el-table-column prop="consultationFee" label="问诊费" width="100">
                    <template #default="scope">
                        ¥{{ scope.row.consultationFee }}
                    </template>
                </el-table-column>
                <el-table-column prop="orderStatus" label="订单状态" width="120">
                    <template #default="scope">
                        <el-tag :type="getConsultationStatusType(scope.row.orderStatus)">
                            {{ scope.row.orderStatus }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="180">
                    <template #default="scope">
                        <el-button type="primary" link @click="viewOrderDetail(scope.row)">
                            查看详情
                        </el-button>
                        <el-button v-if="scope.row.orderStatus === '已取消'" type="danger" link
                            @click="handleDelete(scope.row)" style="margin-left: 10px;">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination v-model:current-page="pagination.pageIndex" v-model:page-size="pagination.pageSize"
                    :page-sizes="[4, 8, 12, 16]" :total="pagination.total"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Grid } from '@element-plus/icons-vue'
import { formatDateTime, getOrderStatusText, getOrderStatusType } from '@/utils/format'
import { consultationApi } from '@/api/order'
import type { ConsultationOrder, ConsultationQueryParams } from '@/types/order'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeStatus = ref('all')
const orderList = ref<ConsultationOrder[]>([])
const allOrderList = ref<ConsultationOrder[]>([]) // 存储所有数据，用于客户端筛选

// 筛选表单
const filterForm = reactive<ConsultationQueryParams>({
    pageIndex: 1,
    pageSize: 4,
    orderNumber: '',
    patientName: '',
    doctorName: '',
    orderStatus: '',
    consultationMethod: '',
    beginTime: '',
    endTime: ''
})

// 分页信息
const pagination = reactive({
    pageIndex: 1,
    pageSize: 4,
    total: 0
})

// 获取订单列表
const getOrderList = async () => {
    loading.value = true
    try {
        // 构建查询参数
        const params: ConsultationQueryParams = {
            pageIndex: 1, // 总是从第一页开始
            pageSize: 100, // 使用较大的页面大小获取更多数据
            orderNumber: filterForm.orderNumber || undefined,
            patientName: filterForm.patientName || undefined,
            doctorName: filterForm.doctorName || undefined,
            orderStatus: filterForm.orderStatus || undefined, // 不在API层面筛选状态
            consultationMethod: filterForm.consultationMethod || undefined,
            begin: filterForm.beginTime || undefined,
            end: filterForm.endTime || undefined
        }

        console.log('查询参数:', params)

        const response = await consultationApi.getConsultationOrders(params)
        console.log('API响应:', response.data)

        if (response.data.code === 200) {
            // 存储全量数据
            allOrderList.value = response.data.data.pageData || []

            // 应用状态筛选
            applyStatusFilter()

            ElMessage.success('获取问诊单列表成功')
        } else {
            ElMessage.error(response.data.message || '获取问诊单列表失败')
            orderList.value = []
            allOrderList.value = []
            pagination.total = 0
        }
    } catch (error: any) {
        console.error('获取问诊单列表失败:', error)
        ElMessage.error('获取问诊单列表失败')
        orderList.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 获取API查询用的订单状态
const getOrderStatusForAPI = () => {
    // 如果是"全部"标签，优先使用筛选表单中的状态，否则不传状态参数
    if (activeStatus.value === 'all') {
        return filterForm.orderStatus || undefined
    }

    // 根据当前选中的状态标签，返回对应的中文状态名
    const statusMap: Record<string, string> = {
        'pending': '待支付',
        'completed': '已完成',
        'cancelled': '已取消',
        'inprogress': '进行中',
        'refunded': '已退诊'
    }

    const chineseStatus = statusMap[activeStatus.value]
    console.log(`状态标签: ${activeStatus.value} -> API参数: ${chineseStatus}`)
    return chineseStatus
}

// 获取问诊单状态类型
const getConsultationStatusType = (status: string) => {
    const statusTypeMap: Record<string, string> = {
        '待支付': 'warning',
        '进行中': 'primary',
        '已完成': 'success',
        '已取消': 'info',
        '已退诊': 'danger'
    }
    return statusTypeMap[status] || 'info'
}

// 重置筛选条件
const handleReset = () => {
    filterForm.orderNumber = ''
    filterForm.patientName = ''
    filterForm.doctorName = ''
    filterForm.orderStatus = ''
    filterForm.consultationMethod = ''
    filterForm.beginTime = ''
    filterForm.endTime = ''
    activeStatus.value = 'all'
    pagination.pageIndex = 1
    getOrderList()
}

// 筛选处理
const handleFilter = () => {
    pagination.pageIndex = 1
    getOrderList()
}



// 分页大小变化
const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageIndex = 1

    if (allOrderList.value.length > 0) {
        applyStatusFilter()
    } else {
        getOrderList()
    }
}

// 当前页变化
const handleCurrentChange = (page: number) => {
    pagination.pageIndex = page

    if (allOrderList.value.length > 0) {
        applyStatusFilter()
    } else {
        getOrderList()
    }
}

// 获取指定状态的数据数量
const getStatusCount = (status: string) => {
    if (status === 'all') {
        return allOrderList.value.length
    }
    return allOrderList.value.filter(order => order.orderStatus === status).length
}

// 应用状态筛选
const applyStatusFilter = () => {
    let filteredData: ConsultationOrder[] = []

    if (activeStatus.value === 'all') {
        filteredData = [...allOrderList.value]
    } else {
        filteredData = allOrderList.value.filter(order =>
            order.orderStatus === activeStatus.value
        )
    }

    // 应用客户端分页
    const startIndex = (pagination.pageIndex - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    orderList.value = filteredData.slice(startIndex, endIndex)

    // 更新总数
    pagination.total = filteredData.length
}

// 状态标签切换处理
const handleStatusChange = (tab: any) => {
    console.log('状态切换:', tab.paneName || tab.name)
    // 重置页码到第一页
    pagination.pageIndex = 1
    // 重新获取数据
    getOrderList()
}

// 查看订单详情
const viewOrderDetail = (row: any) => {
    router.push({
        name: 'ConsultationFan',
        query: { orderNumber: row.orderNumber }
    })
}

// 删除问诊单
const handleDelete = async (row: ConsultationOrder) => {
    try {
        // 确认删除
        await ElMessageBox.confirm(
            `确定要删除订单号为 "${row.orderNumber}" 的问诊单吗？`,
            '删除确认',
            {
                confirmButtonText: '确定删除',
                cancelButtonText: '取消',
                type: 'warning',
                confirmButtonClass: 'el-button--danger'
            }
        )

        // 执行删除
        const response = await consultationApi.deleteConsultationOrder(row.orderNumber)

        if (response.data.code === 200) {
            ElMessage.success('删除成功')
            // 重新获取数据
            getOrderList()
        } else {
            ElMessage.error(response.data.message || '删除失败')
        }
    } catch (error: any) {
        if (error !== 'cancel') {
            console.error('删除问诊单失败:', error)
            ElMessage.error('删除失败')
        }
    }
}

// 监听状态变化
watch(activeStatus, () => {
    pagination.pageIndex = 1
    if (allOrderList.value.length > 0) {
        applyStatusFilter()
    } else {
        getOrderList()
    }
})

// 组件挂载时获取数据
onMounted(() => {
    getOrderList()
})
</script>

<style scoped>
.consultation-management {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header h1 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.filter-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
}

.filter-header .el-icon {
    margin-right: 8px;
    color: #409eff;
}

.filter-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.filter-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.date-label {
    font-weight: 500;
    color: #666;
    margin-right: 10px;
}

.data-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-weight: 600;
    color: #333;
}

.data-header .el-icon {
    margin-right: 8px;
    color: #409eff;
}

.status-tabs {
    margin-bottom: 20px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

:deep(.el-tabs__item) {
    font-size: 14px;
    padding: 0 20px;
}

:deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
}

:deep(.el-table th) {
    background-color: #fafafa;
    color: #333;
    font-weight: 600;
}

:deep(.el-table td) {
    padding: 12px 0;
}

:deep(.el-button--link) {
    padding: 0;
    font-size: 14px;
}
</style>