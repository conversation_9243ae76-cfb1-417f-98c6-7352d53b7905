using MediatR;
using WorkOrder.ErrorCode;

namespace WorkOrder.API.Write.Application.command
{
    /// <summary>
    /// 删除问诊单命令
    /// </summary>
    public class DeleteConsultationOrderCommand : IRequest<APIResult<bool>>
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

      
    }

    /// <summary>
    /// 批量删除问诊单命令
    /// </summary>
    public class BatchDeleteConsultationOrderCommand : IRequest<APIResult<bool>>
    {
        /// <summary>
        /// 订单编号列表
        /// </summary>
        public List<string> OrderNumbers { get; set; } = new List<string>();

      
    }
}
