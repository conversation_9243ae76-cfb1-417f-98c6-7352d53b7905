﻿using Microsoft.EntityFrameworkCore;
using System.Security.Cryptography;
using WorkOrder.Domain;

namespace WorkOrder.Infrastucture
{
  public class MyDbcontext : DbContext
  {
    public MyDbcontext(DbContextOptions options) : base(options)
    {
    }
    #region RBAC
    /// <summary>
    /// 用户信息
    /// </summary>
    public DbSet<UserModel> Users { get; set; }
    /// <summary>
    /// 角色信息
    /// </summary>
    public DbSet<RoleModel> Roles { get; set; }
    /// <summary>
    /// 用户角色
    /// </summary>
    public DbSet<UserRoleModel> UserRoles { get; set; }
    /// <summary>
    /// 权限信息
    /// </summary>
    public DbSet<PermissionModel> Permissions { get; set; }
    /// <summary>
    /// 角色权限
    /// </summary>
    public DbSet<RolePermissionModel> RolePermissions { get; set; }
    #endregion

    #region 订单
    public DbSet<ConsultationOrder> ConsultationOrders { get; set; }
    public DbSet<MedicineOrder> MedicineOrders { get; set; }
    public DbSet<PrescriptionOrder> PrescriptionOrders { get; set; }
    public DbSet<RefundApplication> RefundApplications { get; set; }
    public DbSet<RegistrationOrder> RegistrationOrders { get; set; }
    #endregion

  }
}
