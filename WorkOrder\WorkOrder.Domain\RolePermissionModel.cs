using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WorkOrder.Domain
{
    /// <summary>
    /// 角色权限表
    /// </summary>
    [Table("RolePermission")]
    public class RolePermissionModel : BaseEntity
    {
        /// <summary>
        /// 权限Id
        /// </summary>
        public long PermissionId { get; set; }
        
        /// <summary>
        /// 角色Id
        /// </summary>
        public long RoleId { get; set; }
    }
} 