using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WorkOrder.Domain
{
    /// <summary>
    /// 处方流转订单实体类
    /// </summary>
    [Table("PrescriptionOrder")]
    public class PrescriptionOrder
    {
        /// <summary>
        /// 处方订单编号（主键）- 格式：PRE + 年月日 + 6位序号，如：PRE20240101000001
        /// </summary>
        [Key]
        public  string OrderNumber { get; set; }

        /// <summary>
        /// 处方编号
        /// </summary>        public required string PrescriptionNumber { get; set; }

        /// <summary>
        /// 订单状态（待支付、已完成、已失效）
        /// </summary>

        public  string OrderStatus { get; set; }

        /// <summary>
        /// 处方单状态（审核通过、药店购药、在线配送、已核准发药、未核准发药、核准发药等）
        /// </summary>
        public  string PrescriptionStatus { get; set; }

        /// <summary>
        /// 购药方式（药店购药、在线配送等）
        /// </summary>

        public string? PurchaseMethod { get; set; }

        /// <summary>
        /// 发药状态（已核准发药、未核准发药、核准发药等）
        /// </summary>

        public string? DispenseStatus { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>

        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>

        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>
        public decimal ActualPayment { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>

        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>

        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public  string PatientName { get; set; }

        /// <summary>
        /// 患者性别
        /// </summary>
        public  string PatientGender { get; set; }

        /// <summary>
        /// 患者年龄
        /// </summary>

        public int PatientAge { get; set; }

        /// <summary>
        /// 患者手机号
        /// </summary>
        public  string PatientPhone { get; set; }

        /// <summary>
        /// 所患疾病
        /// </summary>
        public string? Disease { get; set; }

        /// <summary>
        /// 肝功能状态
        /// </summary>
        public string? LiverFunction { get; set; }

        /// <summary>
        /// 肾功能状态
        /// </summary>
        public string? KidneyFunction { get; set; }

        /// <summary>
        /// 过敏史
        /// </summary>
        public string? AllergyHistory { get; set; }

        /// <summary>
        /// 生育计划或状态
        /// </summary>
        public string? PregnancyStatus { get; set; }

        /// <summary>
        /// 病情描述
        /// </summary>
        public string? SymptomDescription { get; set; }

        /// <summary>
        /// 已选药品及用药情况
        /// </summary>
        public string? SelectedMedications { get; set; }

        /// <summary>
        /// 处方信息编号
        /// </summary>
        public  string PrescriptionInfoNumber { get; set; }

        /// <summary>
        /// 开单时间
        /// </summary>
        public DateTime PrescriptionDate { get; set; }

        /// <summary>
        /// 处方单状态描述
        /// </summary>
        public string? PrescriptionStatusDescription { get; set; }

        /// <summary>
        /// 医生姓名
        /// </summary>
        public  string DoctorName { get; set; }

        /// <summary>
        /// 科室
        /// </summary>
        public  string Department { get; set; }

        /// <summary>
        /// 初步诊断
        /// </summary>
        public string? PreliminaryDiagnosis { get; set; }

        /// <summary>
        /// 处理意见
        /// </summary>
        public string? TreatmentAdvice { get; set; }

        /// <summary>
        /// 处方详情（JSON格式存储药品信息）
        /// </summary>
        public string? PrescriptionDetails { get; set; }

        /// <summary>
        /// 用户名（提交订单的用户）
        /// </summary>
        public string UserName { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
        public bool IsDelete { get; set; } = false;
    }

    /// <summary>
    /// 处方药品明细实体类
    /// </summary>
    [Table("PrescriptionMedicines")]
    public class PrescriptionMedicine
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 处方订单编号
        /// </summary>

        public required string OrderNumber { get; set; }

        /// <summary>
        /// 药品名称
        /// </summary>

        public required string MedicineName { get; set; }

        /// <summary>
        /// 药品规格
        /// </summary>

        public required string Specification { get; set; }

        /// <summary>
        /// 数量
        /// </summary>

        public int Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 用药说明
        /// </summary>

        public string? Usage { get; set; }

        /// <summary>
        /// 药品类型（处方药、非处方药等）
        /// </summary>

        public string? MedicineType { get; set; }

        /// <summary>
        /// 是否为处方药
        /// </summary>
        public bool IsPrescriptionDrug { get; set; } = true;

        /// <summary>
        /// 外键关联处方订单
        /// </summary>
        [ForeignKey("OrderNumber")]
        public PrescriptionOrder? PrescriptionOrder { get; set; }
        
    }
}
