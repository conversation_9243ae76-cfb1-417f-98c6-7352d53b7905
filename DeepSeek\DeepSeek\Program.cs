﻿using Microsoft.SemanticKernel;
using Microsoft.SemanticKernel.Embeddings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using System.Text.Json;
using System.Text;
using OllamaSharp;
using Microsoft.Extensions.AI;

namespace DeepSeekRAG
{
    // 文档块类
    public class DocumentChunk
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Content { get; set; } = string.Empty;
        public string Source { get; set; } = string.Empty;
        public int ChunkIndex { get; set; }
        public ReadOnlyMemory<float> Embedding { get; set; }
    }

    // 文档处理器
    public class DocumentProcessor
    {
        private readonly ITextEmbeddingGenerationService _embeddingService;
        private readonly ILogger<DocumentProcessor> _logger;

        public DocumentProcessor(ITextEmbeddingGenerationService embeddingService, ILogger<DocumentProcessor> logger)
        {
            _embeddingService = embeddingService;
            _logger = logger;
        }

        // 加载文档
        public async Task<string> LoadDocumentAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"文件不存在: {filePath}");
                }

                var content = await File.ReadAllTextAsync(filePath, Encoding.UTF8);
                _logger.LogInformation($"成功加载文档: {filePath}, 长度: {content.Length}");
                return content;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"加载文档失败: {filePath}");
                throw;
            }
        }

        // 文档分块
        public List<DocumentChunk> ChunkDocument(string content, string source, int chunkSize = 500, int overlap = 50)
        {
            var chunks = new List<DocumentChunk>();

            if (string.IsNullOrWhiteSpace(content))
            {
                return chunks;
            }

            var sentences = content.Split(new[] { '.', '!', '?', '。', '！', '？' }, StringSplitOptions.RemoveEmptyEntries);
            var currentChunk = new StringBuilder();
            var chunkIndex = 0;

            foreach (var sentence in sentences)
            {
                var trimmedSentence = sentence.Trim();
                if (string.IsNullOrEmpty(trimmedSentence))
                    continue;

                // 如果添加这个句子会超过块大小，先保存当前块
                if (currentChunk.Length + trimmedSentence.Length > chunkSize && currentChunk.Length > 0)
                {
                    chunks.Add(new DocumentChunk
                    {
                        Content = currentChunk.ToString().Trim(),
                        Source = source,
                        ChunkIndex = chunkIndex++
                    });

                    // 保留重叠部分
                    var chunkText = currentChunk.ToString();
                    var overlapStart = Math.Max(0, chunkText.Length - overlap);
                    currentChunk.Clear();
                    currentChunk.Append(chunkText.Substring(overlapStart));
                }

                currentChunk.Append(trimmedSentence + ". ");
            }

            // 添加最后一个块
            if (currentChunk.Length > 0)
            {
                chunks.Add(new DocumentChunk
                {
                    Content = currentChunk.ToString().Trim(),
                    Source = source,
                    ChunkIndex = chunkIndex
                });
            }

            _logger.LogInformation($"文档分块完成: {chunks.Count} 个块");
            return chunks;
        }

        // 为文档块生成向量
        public async Task<List<DocumentChunk>> GenerateEmbeddingsAsync(List<DocumentChunk> chunks)
        {
            _logger.LogInformation($"开始为 {chunks.Count} 个文档块生成向量...");

            var tasks = chunks.Select(async chunk =>
            {
                try
                {
                    var embedding = await _embeddingService.GenerateEmbeddingAsync(chunk.Content);
                    chunk.Embedding = embedding;
                    return chunk;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"为块 {chunk.Id} 生成向量失败");
                    return chunk;
                }
            });

            var results = await Task.WhenAll(tasks);
            _logger.LogInformation("向量生成完成");
            return results.ToList();
        }
    }

    // 向量存储
    public class VectorStore
    {
        private readonly List<DocumentChunk> _chunks;
        private readonly ILogger<VectorStore> _logger;

        public VectorStore(ILogger<VectorStore> logger)
        {
            _chunks = new List<DocumentChunk>();
            _logger = logger;
        }

        // 添加文档块
        public void AddChunks(IEnumerable<DocumentChunk> chunks)
        {
            _chunks.AddRange(chunks);
            _logger.LogInformation($"添加了 {chunks.Count()} 个文档块，总计 {_chunks.Count} 个块");
        }

        // 计算余弦相似度
        private static float CosineSimilarity(ReadOnlyMemory<float> vector1, ReadOnlyMemory<float> vector2)
        {
            var v1 = vector1.Span;
            var v2 = vector2.Span;

            if (v1.Length != v2.Length)
                return 0f;

            float dotProduct = 0f;
            float norm1 = 0f;
            float norm2 = 0f;

            for (int i = 0; i < v1.Length; i++)
            {
                dotProduct += v1[i] * v2[i];
                norm1 += v1[i] * v1[i];
                norm2 += v2[i] * v2[i];
            }

            if (norm1 == 0f || norm2 == 0f)
                return 0f;

            return dotProduct / (MathF.Sqrt(norm1) * MathF.Sqrt(norm2));
        }

        // 搜索相似文档
        public Task<List<DocumentChunk>> SearchSimilarAsync(ReadOnlyMemory<float> queryEmbedding, int topK = 5, float threshold = 0.7f)
        {
            var similarities = new List<(DocumentChunk chunk, float similarity)>();

            foreach (var chunk in _chunks)
            {
                if (chunk.Embedding.IsEmpty)
                    continue;

                var similarity = CosineSimilarity(queryEmbedding, chunk.Embedding);
                if (similarity >= threshold)
                {
                    similarities.Add((chunk, similarity));
                }
            }

            var results = similarities
                .OrderByDescending(x => x.similarity)
                .Take(topK)
                .Select(x => x.chunk)
                .ToList();

            _logger.LogInformation($"搜索到 {results.Count} 个相似文档块");
            return Task.FromResult(results);
        }

        // 获取所有文档块
        public List<DocumentChunk> GetAllChunks()
        {
            return _chunks.ToList();
        }

        // 清空存储
        public void Clear()
        {
            _chunks.Clear();
            _logger.LogInformation("向量存储已清空");
        }
    }

    // RAG检索器
    public class RAGRetriever
    {
        private readonly VectorStore _vectorStore;
        private readonly ITextEmbeddingGenerationService _embeddingService;
        private readonly ILogger<RAGRetriever> _logger;

        public RAGRetriever(VectorStore vectorStore, ITextEmbeddingGenerationService embeddingService, ILogger<RAGRetriever> logger)
        {
            _vectorStore = vectorStore;
            _embeddingService = embeddingService;
            _logger = logger;
        }

        // 检索相关文档
        public async Task<List<DocumentChunk>> RetrieveAsync(string query, int topK = 5, float threshold = 0.7f)
        {
            try
            {
                _logger.LogInformation($"开始检索查询: {query}");

                // 生成查询向量
                var queryEmbedding = await _embeddingService.GenerateEmbeddingAsync(query);

                // 搜索相似文档
                var results = await _vectorStore.SearchSimilarAsync(queryEmbedding, topK, threshold);

                _logger.LogInformation($"检索完成，找到 {results.Count} 个相关文档");
                return results;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"检索失败: {query}");
                return new List<DocumentChunk>();
            }
        }

        // 格式化检索结果为上下文
        public string FormatContext(List<DocumentChunk> chunks)
        {
            if (!chunks.Any())
            {
                return "没有找到相关的文档内容。";
            }

            var context = new StringBuilder();
            context.AppendLine("以下是相关的文档内容：");
            context.AppendLine();

            for (int i = 0; i < chunks.Count; i++)
            {
                var chunk = chunks[i];
                context.AppendLine($"文档片段 {i + 1} (来源: {chunk.Source}):");
                context.AppendLine(chunk.Content);
                context.AppendLine();
            }

            return context.ToString();
        }
    }
    
    // DeepSeek API客户端
    public class DeepSeekClient
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;
        private readonly ILogger<DeepSeekClient> _logger;
        private const string BaseUrl = "http://localhost:11434";

        public DeepSeekClient(HttpClient httpClient, string apiKey, ILogger<DeepSeekClient> logger)
        {
            _httpClient = httpClient;
            _apiKey = apiKey;
            _logger = logger;
        }

//        public async Task<string> GenerateResponseAsync(string prompt, string context)
//        {
//            try
//            {
//                var systemPrompt = @"你是一个智能助手，请基于提供的上下文信息来回答用户的问题。
//                如果上下文中没有相关信息，请明确说明。请确保回答准确、有用且基于事实。";

//                var userPrompt = $@"上下文信息：
//{context}

//用户问题：{prompt}

//请基于上述上下文信息回答用户的问题。";

//                var requestBody = new
//                {
//                    model = "deepseek-r1:8b",
//                    messages = new[]
//                    {
//                        new { role = "system", content = systemPrompt },
//                        new { role = "user", content = userPrompt }
//                    },
//                    temperature = 0.7,
//                    max_tokens = 2000
//                };

//                var json = JsonSerializer.Serialize(requestBody);
//                var content = new StringContent(json, Encoding.UTF8, "application/json");

//                _httpClient.DefaultRequestHeaders.Clear();
//                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

//                _logger.LogInformation("发送请求到DeepSeek API...");
//                var response = await _httpClient.PostAsync(BaseUrl, content);

//                if (!response.IsSuccessStatusCode)
//                {
//                    var errorContent = await response.Content.ReadAsStringAsync();
//                    _logger.LogError($"API请求失败: {response.StatusCode}, {errorContent}");
//                    return GenerateFallbackResponse(context);
//                }

//                var responseContent = await response.Content.ReadAsStringAsync();
//                var responseJson = JsonSerializer.Deserialize<JsonElement>(responseContent);

//                if (responseJson.TryGetProperty("choices", out var choices) && choices.GetArrayLength() > 0)
//                {
//                    var firstChoice = choices[0];
//                    if (firstChoice.TryGetProperty("message", out var message) &&
//                        message.TryGetProperty("content", out var messageContent))
//                    {
//                        var result = messageContent.GetString() ?? "无法生成回答。";
//                        _logger.LogInformation("成功生成回答");
//                        return result;
//                    }
//                }

//                _logger.LogWarning("API响应格式异常");
//                return GenerateFallbackResponse(context);
//            }
//            catch (Exception ex)
//            {
//                _logger.LogError(ex, "调用DeepSeek API时发生错误");
//                return GenerateFallbackResponse(context);
//            }
//        }
        public async Task<string> AskQuestion(string question)
        {
            var chatClient = new OllamaApiClient(new Uri(BaseUrl), "deepseek-r1:8b");

            // 使用 await foreach 遍历每个 ChatResponseUpdate
            var responseBuilder = new List<string>();
            await foreach (var update in chatClient.GetStreamingResponseAsync(question))
            {
                responseBuilder.Add(update.ToString());
            }
            var response = string.Join("", responseBuilder);
            return response;
        }
        private string GenerateFallbackResponse(string context)
        {
            if (string.IsNullOrWhiteSpace(context) || context.Contains("没有找到相关的文档内容"))
            {
                return "抱歉，我在知识库中没有找到相关信息来回答您的问题。请尝试使用不同的关键词重新提问。";
            }

            // 提取文档内容进行简单的智能处理
            var lines = context.Split('\n', StringSplitOptions.RemoveEmptyEntries);
            var contentLines = lines.Where(line => !line.StartsWith("文档片段") && !line.StartsWith("以下是相关") && !string.IsNullOrWhiteSpace(line)).ToList();

            if (contentLines.Any())
            {
                var mainContent = string.Join(" ", contentLines);
                return $"根据知识库内容：\n\n{mainContent}\n\n" +
                       "💡 提示：当前使用的是基于文档检索的回答模式。要获得更智能的AI生成回答，请：\n" +
                       "1. 充值DeepSeek账户余额\n" +
                       "2. 或联系DeepSeek获取免费试用额度";
            }

            return $"基于知识库内容，我找到了以下相关信息：\n\n{context}\n\n" +
                   "注意：由于API账户余额不足，这是基于检索到的文档内容的直接回复。";
        }
    }

    // RAG生成器
    public class RAGGenerator
    {
        private readonly RAGRetriever _retriever;
        private readonly DeepSeekClient _deepSeekClient;
        private readonly ILogger<RAGGenerator> _logger;

        public RAGGenerator(RAGRetriever retriever, DeepSeekClient deepSeekClient, ILogger<RAGGenerator> logger)
        {
            _retriever = retriever;
            _deepSeekClient = deepSeekClient;
            _logger = logger;
        }

        public async Task<string> GenerateAnswerAsync(string question, int topK = 5, float threshold = 0.7f)
        {
            try
            {
                _logger.LogInformation($"开始生成问题的回答: {question}");

                // 1. 检索相关文档
                var relevantChunks = await _retriever.RetrieveAsync(question, topK, threshold);

                // 2. 格式化上下文
                var context = _retriever.FormatContext(relevantChunks);

              question =$@"{context}根据获取到的信息回答问题：{question}";

        // 3. 生成回答
        var answer = await _deepSeekClient.AskQuestion(question);

                _logger.LogInformation("成功生成回答");
                return answer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"生成回答失败: {question}");
                return "抱歉，生成回答时出现错误。";
            }
        }
    }

    // 主程序类
    public class Program
    {
        private static async Task Main(string[] args)
        {
            // 配置日志
            using var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Information));

            var logger = loggerFactory.CreateLogger<Program>();

            try
            {
                logger.LogInformation("=== DeepSeek RAG 系统启动 ===");

                // 检查是否要运行API测试
                if (args.Length > 0 && args[0] == "--test-api")
                {
                    await ApiConnectionTest.RunDiagnostics();
                    return;
                }

                // 配置参数
                var apiKey = Environment.GetEnvironmentVariable("DEEPSEEK_API_KEY") ??
                            "sk-eqyoixrhefebaiqpuzkwkqwszjwjntcueltxtubfvylfmvpg"; // 请替换为您的API密钥

                if (apiKey == "sk-eqyoixrhefebaiqpuzkwkqwszjwjntcueltxtubfvylfmvpg")
                {
                    logger.LogWarning("请设置DEEPSEEK_API_KEY环境变量或在代码中配置API密钥");
                }

                // 创建服务
                var httpClient = new HttpClient();

                logger.LogInformation("正在初始化嵌入服务...");
                var embeddingService = new SimpleEmbeddingService();

                // 创建服务实例
                var documentProcessor = new DocumentProcessor(embeddingService, loggerFactory.CreateLogger<DocumentProcessor>());
                var vectorStore = new VectorStore(loggerFactory.CreateLogger<VectorStore>());
                var retriever = new RAGRetriever(vectorStore, embeddingService, loggerFactory.CreateLogger<RAGRetriever>());
                var deepSeekClient = new DeepSeekClient(httpClient, apiKey, loggerFactory.CreateLogger<DeepSeekClient>());
                var ragGenerator = new RAGGenerator(retriever, deepSeekClient, loggerFactory.CreateLogger<RAGGenerator>());

                // 演示用法
                await DemoRAGSystem(documentProcessor, vectorStore, ragGenerator, logger);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "程序运行出错");
            }

            logger.LogInformation("程序结束，按任意键退出...");
            Console.ReadKey();
        }

        private static async Task DemoRAGSystem(
            DocumentProcessor documentProcessor,
            VectorStore vectorStore,
            RAGGenerator ragGenerator,
            ILogger logger)
        {
            logger.LogInformation("=== RAG 系统演示 ===");

            // 加载示例文档文件
            var sampleDocuments = new Dictionary<string, string>();

            // 尝试加载示例文档文件
            var docFiles = new[] { "sample_documents/ai_introduction.txt", "sample_documents/rag_system.txt" };
            foreach (var file in docFiles)
            {
                try
                {
                    if (File.Exists(file))
                    {
                        var content = await File.ReadAllTextAsync(file);
                        sampleDocuments[Path.GetFileName(file)] = content;
                        logger.LogInformation($"加载文档文件: {file}");
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex, $"无法加载文档文件: {file}");
                }
            }

            // 如果没有找到文件，使用内置示例
            if (!sampleDocuments.Any())
            {
                sampleDocuments = new Dictionary<string, string>
                {
                    ["技术文档.txt"] = @"
                    人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。
                    机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。
                    深度学习是机器学习的一个子集，使用神经网络来模拟人脑的工作方式。
                    自然语言处理（NLP）是AI的一个领域，专注于计算机与人类语言之间的交互。
                    ",
                    ["产品介绍.txt"] = @"
                    我们的RAG系统结合了检索和生成技术，能够基于文档库回答用户问题。
                    系统支持多种文档格式，包括文本文件、PDF和Word文档。
                    向量化技术确保了高效的语义搜索和相关性匹配。
                    DeepSeek模型提供了强大的自然语言生成能力。
                    "
                };
                logger.LogInformation("使用内置示例文档");
            }

            try
            {
                // 处理示例文档
                logger.LogInformation("正在处理示例文档...");

                foreach (var doc in sampleDocuments)
                {
                    var chunks = documentProcessor.ChunkDocument(doc.Value, doc.Key);
                    logger.LogInformation($"文档 {doc.Key} 分为 {chunks.Count} 个块");

                    // 生成向量嵌入
                    var chunksWithEmbeddings = await documentProcessor.GenerateEmbeddingsAsync(chunks);
                    vectorStore.AddChunks(chunksWithEmbeddings);
                }

                // 交互式问答
                logger.LogInformation("\n=== 开始交互式问答 ===");
                logger.LogInformation("输入问题（输入 'quit' 退出）：");

                while (true)
                {
                    Console.Write("\n问题: ");
                    var question = Console.ReadLine();

                    if (string.IsNullOrWhiteSpace(question) || question.ToLower() == "quit")
                    {
                        break;
                    }

                    logger.LogInformation($"处理问题: {question}");

                    // 使用完整的RAG流程，降低相似度阈值以便找到更多相关文档
                    var answer = await ragGenerator.GenerateAnswerAsync(question, topK: 5, threshold: 0.0f);

                    Console.WriteLine($"\n回答: {answer}");
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "演示过程中出错");
            }
        }

        // 模拟回答生成（用于演示）
        private static string GenerateMockAnswer(string question, List<DocumentChunk> allChunks)
        {
            var relevantChunks = allChunks
                .Where(chunk => chunk.Content.Contains("AI") ||
                               chunk.Content.Contains("人工智能") ||
                               chunk.Content.Contains("机器学习") ||
                               chunk.Content.Contains("RAG") ||
                               chunk.Content.Contains("系统"))
                .Take(2)
                .ToList();

            if (!relevantChunks.Any())
            {
                return "抱歉，我在文档中没有找到相关信息来回答您的问题。";
            }

            var context = string.Join("\n", relevantChunks.Select(c => c.Content));
            return $"基于文档内容，{question}的相关信息如下：\n\n{context}\n\n" +
                   "注意：这是一个模拟回答。完整的RAG系统需要配置嵌入服务和DeepSeek API密钥。";
        }
    }
}






























