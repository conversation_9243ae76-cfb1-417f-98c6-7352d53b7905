﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Text;
using System.Threading.Tasks;
using WorkOrder.Infrastucture;


namespace WorkOrder.Infrastucture
{
    /// <summary>
    /// 仓储实现类
    /// </summary>
    /// <typeparam name="T">实体基类泛型</typeparam>
    public class BaseRepository<T> : IBaseRepository<T> where T : class
    {
        /// <summary>
        /// 声明上下文字段
        /// </summary>
        private readonly MyDbcontext db;

        /// <summary>
        /// EFCore上下文
        /// </summary>
        public MyDbcontext Context { get { return db; } }

        /// <summary>
        /// 构造函数注入资源
        /// </summary>
        /// <param name="db">上下文</param>
        public BaseRepository(MyDbcontext db)
        {
            this.db = db;
        }

        /// <summary>
        /// 泛型添加方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        public async Task<int> AddAsync(T t)
        {
            try
            {
                await db.Set<T>().AddAsync(t);
                return await db.SaveChangesAsync();
            }
            catch (Exception)
            {

                throw;
            }
        }
        /// <summary>
        /// 泛型查询方法
        /// </summary>
        /// <returns>满足条件的数据</returns>
        public IQueryable<T> GetAll()
        {
            try
            {
                return db.Set<T>().AsQueryable();
            }
            catch (Exception)
            {

                throw;
            }
        }

        /// <summary>
        /// 泛型通过id查单条数据方法
        /// </summary>
        /// <param name="id">主键编号</param>
        /// <returns>单条数据</returns>
        public async Task<T> GetModel(long id)
        {
            try
            {
                return await db.Set<T>().FindAsync(id);
            }
            catch (Exception)
            {

                throw;
            }
        }

        /// <summary>
        /// 泛型修改方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        public async Task<int> UpdateAsync(T t)
        {
            try
            {
                db.Set<T>().Update(t);
                return await db.SaveChangesAsync();
            }
            catch (Exception)
            {

                throw;
            }
        }
        /// <summary>
        /// 泛型批量修改方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        public async Task<int> UpdateRangAsync(List<T> t)
        {
            try
            {
                db.Set<T>().UpdateRange(t);
                return await db.SaveChangesAsync();
            }
            catch (Exception)
            {

                throw;
            }
        }

        /// <summary>
        /// 泛型修改方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        public async Task<int> DeleteAsync(T t)
        {
            db.Set<T>().Remove(t);
            return await db.SaveChangesAsync();
        }
    }
}