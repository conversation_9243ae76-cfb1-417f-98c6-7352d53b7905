﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.7" />
    <PackageReference Include="Microsoft.SemanticKernel" Version="1.61.0" />
    <PackageReference Include="OllamaSharp" Version="5.3.3" />
    <PackageReference Include="System.Text.Json" Version="9.0.7" />
  </ItemGroup>

</Project>
