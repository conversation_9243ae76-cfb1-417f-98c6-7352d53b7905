<template>
    <div class="refund-management">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>退款申请管理</h1>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-header">
                <el-icon>
                    <Grid />
                </el-icon>
                <span>筛选</span>
            </div>
            <div class="filter-content">
                <el-input v-model="filterForm.keyword" placeholder="输入订单号或用户名" style="width: 300px; margin-right: 20px;"
                    clearable />
                <span class="date-label">日期</span>
                <el-date-picker v-model="filterForm.beginTime" type="datetime" placeholder="开始时间"
                    style="width: 200px; margin-right: 10px;" format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss" />
                <el-date-picker v-model="filterForm.endTime" type="datetime" placeholder="结束时间"
                    style="width: 200px; margin-right: 20px;" format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss" />
                <el-button type="primary" @click="handleFilter">筛选</el-button>
                <el-button type="success" @click="generateTestData">加载测试数据</el-button>
            </div>
        </div>

        <!-- 数据列表区域 -->
        <div class="data-section">
            <div class="data-header">
                <el-icon>
                    <Grid />
                </el-icon>
                <span>数据列表</span>
            </div>

            <!-- 状态标签页 -->
            <div class="status-tabs">
                <el-tabs v-model="activeStatus" @tab-click="handleStatusChange">
                    <el-tab-pane label="全部" name="all" />
                    <el-tab-pane label="待审核" name="pending" />
                    <el-tab-pane label="已通过" name="approved" />
                    <el-tab-pane label="已拒绝" name="rejected" />
                    <el-tab-pane label="已退款" name="refunded" />
                </el-tabs>
            </div>

            <!-- 数据表格 -->
            <el-table :data="orderList" style="width: 100%" v-loading="loading">
                <el-table-column prop="refundNumber" label="退款编号" width="180" />
                <el-table-column prop="originalOrderNumber" label="原订单号" width="180" />
                <el-table-column prop="submitTime" label="申请时间" width="180">
                    <template #default="scope">
                        {{ formatDateTime(scope.row.submitTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="userName" label="用户名" width="150" />
                <el-table-column prop="refundAmount" label="退款金额" width="100">
                    <template #default="scope">
                        ¥{{ scope.row.refundAmount }}
                    </template>
                </el-table-column>
                <el-table-column prop="refundReason" label="退款原因" width="150" />
                <el-table-column prop="refundStatus" label="退款状态" width="120">
                    <template #default="scope">
                        <el-tag :type="getRefundStatusType(scope.row.refundStatus)">
                            {{ getRefundStatusText(scope.row.refundStatus) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="scope">
                        <el-button type="primary" link @click="viewRefundDetail(scope.row)">
                            查看详情
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination v-model:current-page="pagination.pageIndex" v-model:page-size="pagination.pageSize"
                    :page-sizes="[4, 8, 12, 16]" :total="pagination.total"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Grid } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeStatus = ref('all')
const orderList = ref<any[]>([])

// 筛选表单
const filterForm = reactive({
    keyword: '',
    beginTime: '',
    endTime: ''
})

// 分页信息
const pagination = reactive({
    pageIndex: 1,
    pageSize: 4,
    total: 0
})

// 退款状态文本映射
const getRefundStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'refunded': '已退款'
    }
    return statusMap[status] || status
}

// 退款状态类型映射
const getRefundStatusType = (status: string) => {
    const typeMap: Record<string, string> = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'refunded': 'info'
    }
    return typeMap[status] || 'info'
}

// 获取退款列表
const getOrderList = async () => {
    loading.value = true
    try {
        // 这里应该调用退款申请的API
        orderList.value = []
        pagination.total = 0
        ElMessage.info('退款申请管理功能开发中...')
    } catch (error: any) {
        console.error('获取退款申请列表失败:', error)
        ElMessage.error('获取退款申请列表失败')
        orderList.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 生成测试数据
const generateTestData = () => {
    const testData = [
        {
            refundNumber: 'REF20241201001',
            originalOrderNumber: 'ORD20241201001',
            refundStatus: 'pending',
            refundAmount: 50.0,
            refundReason: '医生临时有事，无法就诊',
            submitTime: '2024-12-01 10:30:00',
            userName: 'user001'
        }
    ]

    orderList.value = testData
    pagination.total = testData.length
    ElMessage.success('已加载测试数据')
}

// 筛选处理
const handleFilter = () => {
    pagination.pageIndex = 1
    getOrderList()
}

// 状态变化处理
const handleStatusChange = () => {
    pagination.pageIndex = 1
    getOrderList()
}

// 分页大小变化
const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageIndex = 1
    getOrderList()
}

// 当前页变化
const handleCurrentChange = (page: number) => {
    pagination.pageIndex = page
    getOrderList()
}

// 查看退款详情
const viewRefundDetail = (row: any) => {
    router.push({
        name: 'RefundFan',
        query: { refundNumber: row.refundNumber }
    })
}

// 组件挂载时获取数据
onMounted(() => {
    getOrderList()
})
</script>

<style scoped>
.refund-management {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header h1 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.filter-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
}

.filter-header .el-icon {
    margin-right: 8px;
    color: #409eff;
}

.filter-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.date-label {
    font-weight: 500;
    color: #666;
    margin-right: 10px;
}

.data-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-weight: 600;
    color: #333;
}

.data-header .el-icon {
    margin-right: 8px;
    color: #409eff;
}

.status-tabs {
    margin-bottom: 20px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

:deep(.el-tabs__item) {
    font-size: 14px;
    padding: 0 20px;
}

:deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
}

:deep(.el-table th) {
    background-color: #fafafa;
    color: #333;
    font-weight: 600;
}

:deep(.el-table td) {
    padding: 12px 0;
}

:deep(.el-button--link) {
    padding: 0;
    font-size: 14px;
}
</style>