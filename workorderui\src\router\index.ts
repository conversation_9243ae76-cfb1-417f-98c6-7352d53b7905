import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/home',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue'),
    },
    {
      path: '/',
      name: 'main',
      component: () => import('../views/Main.vue'),
      children: [
        // 挂号单管理
        {
          path: '/RegistrationShow',
          name: 'RegistrationShow',
          component: () => import('../views/Order/RegistrationShow.vue'),
        },
        {
          path: '/RegistrationFan',
          name: 'RegistrationFan',
          component: () => import('../views/Order/RegistrationFan.vue'),
        },
        // 问诊单管理
        {
          path: '/ConsultationShow',
          name: 'ConsultationShow',
          component: () => import('../views/Order/ConsultationShow.vue'),
        },
        {
          path: '/ConsultationFan',
          name: 'ConsultationFan',
          component: () => import('../views/Order/ConsultationFan.vue'),
        },
        // 处方流转订单管理
        {
          path: '/PrescriptionShow',
          name: 'PrescriptionShow',
          component: () => import('../views/Order/PrescriptionShow.vue'),
        },
        {
          path: '/PrescriptionFan',
          name: 'PrescriptionFan',
          component: () => import('../views/Order/PrescriptionFan.vue'),
        },
        // 药品订单管理
        {
          path: '/MedicineShow',
          name: 'MedicineShow',
          component: () => import('../views/Order/MedicineShow.vue'),
        },
        {
          path: '/MedicineFan',
          name: 'MedicineFan',
          component: () => import('../views/Order/MedicineFan.vue'),
        },
        // 退款申请
        {
          path: '/RefundShow',
          name: 'RefundShow',
          component: () => import('../views/Order/RefundShow.vue'),
        },
        {
          path: '/RefundFan',
          name: 'RefundFan',
          component: () => import('../views/Order/RefundFan.vue'),
        },
      ],
    },
  ],
})

export default router
