# 测试问诊单状态筛选功能

$baseUrl = "http://localhost:5202"

Write-Host "=== 测试问诊单状态筛选功能 ===" -ForegroundColor Green

# 测试不同状态的查询
$statuses = @(
    @{Name="全部"; Value=""},
    @{Name="待支付"; Value="待支付"},
    @{Name="进行中"; Value="进行中"},
    @{Name="已完成"; Value="已完成"},
    @{Name="已取消"; Value="已取消"},
    @{Name="已退诊"; Value="已退诊"}
)

foreach ($status in $statuses) {
    Write-Host "`n--- 测试状态: $($status.Name) ---" -ForegroundColor Yellow
    
    try {
        $url = "$baseUrl/api/Management/GetConsultationOrder?PageIndex=1&PageSize=5"
        if ($status.Value -ne "") {
            $url += "&OrderStatus=$($status.Value)"
        }
        
        Write-Host "请求URL: $url" -ForegroundColor Cyan
        
        $response = Invoke-RestMethod -Uri $url -Method GET
        
        if ($response.code -eq 200) {
            Write-Host "✓ 查询成功" -ForegroundColor Green
            Write-Host "总数量: $($response.data.totalCount)" -ForegroundColor White
            Write-Host "当前页数据量: $($response.data.pageData.Count)" -ForegroundColor White
            
            if ($response.data.pageData.Count -gt 0) {
                Write-Host "订单状态分布:" -ForegroundColor White
                $statusCount = @{}
                foreach ($order in $response.data.pageData) {
                    $orderStatus = $order.orderStatus
                    if ($statusCount.ContainsKey($orderStatus)) {
                        $statusCount[$orderStatus]++
                    } else {
                        $statusCount[$orderStatus] = 1
                    }
                }
                
                foreach ($key in $statusCount.Keys) {
                    Write-Host "  $key : $($statusCount[$key])个" -ForegroundColor Gray
                }
                
                # 验证筛选结果
                if ($status.Value -ne "") {
                    $correctCount = 0
                    foreach ($order in $response.data.pageData) {
                        if ($order.orderStatus -eq $status.Value) {
                            $correctCount++
                        }
                    }
                    
                    if ($correctCount -eq $response.data.pageData.Count) {
                        Write-Host "✓ 筛选结果正确" -ForegroundColor Green
                    } else {
                        Write-Host "✗ 筛选结果错误: 期望全部为'$($status.Value)'，但有其他状态" -ForegroundColor Red
                    }
                }
            } else {
                Write-Host "无数据" -ForegroundColor Gray
            }
        } else {
            Write-Host "✗ 查询失败: $($response.message)" -ForegroundColor Red
        }
    } catch {
        Write-Host "✗ 请求失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
