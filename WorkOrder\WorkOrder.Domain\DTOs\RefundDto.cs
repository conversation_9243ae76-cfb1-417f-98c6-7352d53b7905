using System.ComponentModel.DataAnnotations;

namespace WorkOrder.Domain.DTOs
{
    /// <summary>
    /// 退款申请数据传输对象
    /// </summary>
    public class RefundApplicationDto
    {
        /// <summary>
        /// 退款申请ID
        /// </summary>
        public string RefundId { get; set; } = string.Empty;

        /// <summary>
        /// 服务编号
        /// </summary>
        public string ServiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 申请状态
        /// </summary>
        public string ApplicationStatus { get; set; } = string.Empty;

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplicationTime { get; set; }

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal OrderAmount { get; set; }

        /// <summary>
        /// 实际退款金额
        /// </summary>
        public decimal ActualRefundAmount { get; set; }

        /// <summary>
        /// 退款方式
        /// </summary>
        public string RefundMethod { get; set; } = string.Empty;

        /// <summary>
        /// 退款类型
        /// </summary>
        public string RefundType { get; set; } = string.Empty;

        /// <summary>
        /// 退款原因
        /// </summary>
        public string RefundReason { get; set; } = string.Empty;

        /// <summary>
        /// 来源
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 处理人员
        /// </summary>
        public string? ProcessorName { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessTime { get; set; }

        /// <summary>
        /// 处理备注
        /// </summary>
        public string? ProcessRemarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }
    }

    /// <summary>
    /// 创建退款申请请求DTO
    /// </summary>
    public class CreateRefundApplicationDto
    {
        /// <summary>
        /// 服务编号
        /// </summary>
        [Required(ErrorMessage = "服务编号不能为空")]
        [StringLength(50, ErrorMessage = "服务编号长度不能超过50个字符")]
        public string ServiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单编号
        /// </summary>
        [Required(ErrorMessage = "订单编号不能为空")]
        [StringLength(50, ErrorMessage = "订单编号长度不能超过50个字符")]
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 订单金额
        /// </summary>
        [Required(ErrorMessage = "订单金额不能为空")]
        [Range(0.01, 99999.99, ErrorMessage = "订单金额必须大于0")]
        public decimal OrderAmount { get; set; }

        /// <summary>
        /// 实际退款金额
        /// </summary>
        [Required(ErrorMessage = "退款金额不能为空")]
        [Range(0.01, 99999.99, ErrorMessage = "退款金额必须大于0")]
        public decimal ActualRefundAmount { get; set; }

        /// <summary>
        /// 退款方式
        /// </summary>
        [Required(ErrorMessage = "退款方式不能为空")]
        [StringLength(50, ErrorMessage = "退款方式长度不能超过50个字符")]
        public string RefundMethod { get; set; } = string.Empty;

        /// <summary>
        /// 退款类型
        /// </summary>
        [Required(ErrorMessage = "退款类型不能为空")]
        [StringLength(50, ErrorMessage = "退款类型长度不能超过50个字符")]
        public string RefundType { get; set; } = string.Empty;

        /// <summary>
        /// 退款原因
        /// </summary>
        [Required(ErrorMessage = "退款原因不能为空")]
        [StringLength(50, ErrorMessage = "退款原因长度不能超过50个字符")]
        public string RefundReason { get; set; } = string.Empty;

        /// <summary>
        /// 来源
        /// </summary>
        [Required(ErrorMessage = "来源不能为空")]
        public string Source { get; set; } = string.Empty;
    }

    /// <summary>
    /// 退款申请查询DTO
    /// </summary>
    public class RefundApplicationQueryDto
    {
        /// <summary>
        /// 申请状态
        /// </summary>
        public string? ApplicationStatus { get; set; }

        /// <summary>
        /// 搜索关键词（患者姓名或手机号）
        /// </summary>
        public string? SearchKeyword { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 来源
        /// </summary>
        public string? Source { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 处理退款申请DTO
    /// </summary>
    public class ProcessRefundApplicationDto
    {
        /// <summary>
        /// 退款申请ID
        /// </summary>
        [Required(ErrorMessage = "退款申请ID不能为空")]
        public string RefundId { get; set; } = string.Empty;

        /// <summary>
        /// 处理结果（已处理、已拒绝）
        /// </summary>
        [Required(ErrorMessage = "处理结果不能为空")]
        public string ProcessResult { get; set; } = string.Empty;

        /// <summary>
        /// 处理人员
        /// </summary>
        [Required(ErrorMessage = "处理人员不能为空")]
        [StringLength(50, ErrorMessage = "处理人员长度不能超过50个字符")]
        public string ProcessorName { get; set; } = string.Empty;

        /// <summary>
        /// 处理备注
        /// </summary>
        [StringLength(500, ErrorMessage = "处理备注长度不能超过500个字符")]
        public string? ProcessRemarks { get; set; }
    }

    /// <summary>
    /// 退款统计DTO
    /// </summary>
    public class RefundStatisticsDto
    {
        /// <summary>
        /// 总申请数
        /// </summary>
        public int TotalApplications { get; set; }

        /// <summary>
        /// 待处理数量
        /// </summary>
        public int PendingCount { get; set; }

        /// <summary>
        /// 已处理数量
        /// </summary>
        public int ProcessedCount { get; set; }

        /// <summary>
        /// 已拒绝数量
        /// </summary>
        public int RejectedCount { get; set; }

        /// <summary>
        /// 总退款金额
        /// </summary>
        public decimal TotalRefundAmount { get; set; }

        /// <summary>
        /// 按来源统计
        /// </summary>
        public List<RefundSourceStatDto> SourceStatistics { get; set; } = new List<RefundSourceStatDto>();
    }

    /// <summary>
    /// 退款来源统计DTO
    /// </summary>
    public class RefundSourceStatDto
    {
        /// <summary>
        /// 来源名称
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 申请数量
        /// </summary>
        public int Count { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal Amount { get; set; }
    }
}
