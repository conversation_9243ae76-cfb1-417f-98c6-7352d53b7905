{"format": 1, "restore": {"D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeekSAG\\DeepSeekSAG.csproj": {}}, "projects": {"D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeekSAG\\DeepSeekSAG.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeekSAG\\DeepSeekSAG.csproj", "projectName": "DeepSeekSAG", "projectPath": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeekSAG\\DeepSeekSAG.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeekSAG\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Dev\\Components\\Offline Packages", "D:\\使用\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.1.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "D:\\Dev\\Components\\System\\Components\\Packages": {}, "D:\\MECHREVO\\Documents": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.302/PortableRuntimeIdentifierGraph.json"}}}}}