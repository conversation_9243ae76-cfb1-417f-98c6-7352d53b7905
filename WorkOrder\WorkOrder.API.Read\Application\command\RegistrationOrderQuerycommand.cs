﻿using MediatR;
using WorkOrder.Domain;
using WorkOrder.ErrorCode;


namespace WorkOrder.API.Read.Application.command
{
    public class RegistrationOrderQuerycommand : IRequest<APIResult<APIPageing<RegistrationOrder>>>
    {
        public string? OrderNumber { get; set; }
        public DateTime? Begin { get; set; }
        public DateTime? End { get; set; }
        public int PageIndex { get; set; } = 1;
        public int PageSize { get; set; } = 4;
    }

}
