# 工单管理系统前端使用说明

## 功能概述

本系统实现了挂号订单管理的前端功能，包括：

1. **挂号订单列表页面** - 显示所有挂号订单，支持筛选、分页
2. **订单详情页面** - 查看单个订单的详细信息，支持数据反填

## 主要功能

### 1. 挂号订单管理页面 (RegistrationShow.vue)

#### 筛选功能
- **关键词搜索**：在"输入订单号或用户名"框中输入关键词进行搜索
- **时间筛选**：选择开始时间和结束时间进行范围筛选
- **筛选按钮**：点击"筛选"按钮应用筛选条件

#### 状态标签页
- **全部**：显示所有状态的订单
- **待支付**：显示待支付状态的订单
- **已完成**：显示已完成状态的订单
- **已取消**：显示已取消状态的订单
- **已退款**：显示已退款状态的订单

#### 数据表格
显示以下字段：
- 订单编号
- 提交时间
- 用户名
- 医生
- 科室
- 挂号费
- 订单状态（带颜色标签）

#### 分页功能
- 显示总页数和总记录数
- 支持页码跳转
- 支持调整每页显示条数（10/20/50/100）

#### 操作功能
- **查看订单**：点击表格中的"查看订单"按钮跳转到详情页面

### 2. 订单详情页面 (RegistrationFan.vue)

#### 页面布局
页面分为三个主要信息区域：

1. **挂号信息**
   - 就诊时间（格式：2020-04-24 周五 下午）
   - 就诊医院
   - 科室
   - 医生
   - 医生职称
   - 医事服务费
   - 挂号单号

2. **订单信息**
   - 订单编号
   - 状态（带颜色标签）
   - 应付金额
   - 优惠券金额
   - 实际支付金额
   - 支付时间

3. **就诊人信息**
   - 姓名
   - 性别
   - 年龄
   - 联系电话
   - 病情描述（如果有）

#### 操作功能
- **返回**：点击右上角"返回"按钮回到列表页面
- **刷新数据**：点击"刷新数据"按钮重新获取订单详情

## 技术特性

### 1. 响应式设计
- 适配不同屏幕尺寸
- 现代化的UI设计
- 良好的用户体验

### 2. 数据反填
- 自动从后端API获取订单详情
- 实时更新页面数据
- 支持数据刷新

### 3. 错误处理
- 网络请求错误提示
- 数据加载状态显示
- 用户友好的错误信息

### 4. 类型安全
- 使用TypeScript确保类型安全
- 完整的类型定义
- 编译时错误检查

## 使用步骤

### 1. 启动项目
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 2. 配置API地址
确保后端API服务正在运行，默认地址为：`http://localhost:5000`

### 3. 访问页面
- 挂号订单列表：`http://localhost:5173/RegistrationShow`
- 订单详情：通过列表页面的"查看订单"按钮访问

### 4. 使用功能
1. 在列表页面使用筛选功能查找订单
2. 点击"查看订单"查看详情
3. 在详情页面查看完整信息
4. 使用"返回"按钮回到列表页面

## 注意事项

1. **API连接**：确保后端API服务正常运行
2. **跨域问题**：如果遇到跨域问题，需要后端配置CORS
3. **数据格式**：确保后端返回的数据格式正确
4. **网络状态**：检查网络连接是否正常

## 故障排除

### 1. 页面无法加载
- 检查后端API服务是否启动
- 确认API地址配置是否正确
- 查看浏览器控制台错误信息

### 2. 数据不显示
- 检查API接口是否正常返回数据
- 确认数据格式是否符合预期
- 查看网络请求状态

### 3. 筛选功能不工作
- 检查筛选参数是否正确
- 确认后端是否支持相应的筛选功能
- 查看API请求参数

## 开发信息

- **框架**：Vue 3 + TypeScript
- **UI库**：Element Plus
- **构建工具**：Vite
- **HTTP客户端**：Axios
- **日期处理**：Moment.js

## 联系支持

如有问题或建议，请联系开发团队。 