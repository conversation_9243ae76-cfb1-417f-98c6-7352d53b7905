import moment from 'moment'

// 订单状态映射
export const ORDER_STATUS_MAP = {
  'pending': '待支付',
  'completed': '已完成',
  'cancelled': '已取消',
  'refunded': '已退款'
} as const

// 订单状态类型映射
export const ORDER_STATUS_TYPE_MAP = {
  'pending': 'warning',
  'completed': 'success',
  'cancelled': 'danger',
  'refunded': 'info'
} as const

/**
 * 格式化日期时间
 * @param dateTime 日期时间字符串
 * @param format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期时间字符串
 */
export const formatDateTime = (dateTime: string | null | undefined, format = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!dateTime) return '未设置'
  return moment(dateTime).format(format)
}

/**
 * 格式化预约时间
 * @param time 预约时间
 * @returns 格式化后的预约时间字符串
 */
export const formatAppointmentTime = (time: string | null | undefined): string => {
  if (!time) return '未设置'
  const date = moment(time)
  const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
  const weekday = weekdays[date.day()]
  const timeSlot = date.hour() < 12 ? '上午' : '下午'
  return `${date.format('YYYY-MM-DD')} ${weekday} ${timeSlot}`
}

/**
 * 获取订单状态文本
 * @param status 订单状态
 * @returns 状态文本
 */
export const getOrderStatusText = (status: string): string => {
  return ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP] || status
}

/**
 * 获取订单状态类型
 * @param status 订单状态
 * @returns 状态类型
 */
export const getOrderStatusType = (status: string): string => {
  return ORDER_STATUS_TYPE_MAP[status as keyof typeof ORDER_STATUS_TYPE_MAP] || 'info'
}

/**
 * 格式化金额
 * @param amount 金额
 * @param currency 货币符号，默认为 '¥'
 * @returns 格式化后的金额字符串
 */
export const formatAmount = (amount: number | null | undefined, currency = '¥'): string => {
  if (amount === null || amount === undefined) return `${currency}0`
  return `${currency}${amount.toFixed(2)}`
}

/**
 * 格式化手机号
 * @param phone 手机号
 * @returns 格式化后的手机号
 */
export const formatPhone = (phone: string): string => {
  if (!phone) return ''
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
} 