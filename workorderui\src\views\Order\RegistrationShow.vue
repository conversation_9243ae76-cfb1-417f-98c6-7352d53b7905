<template>
    <div class="registration-management">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>挂号订单管理</h1>
        </div>

        <!-- 筛选区域 -->
        <div class="filter-section">
            <div class="filter-header">
                <el-icon>
                    <Grid />
                </el-icon>
                <span>筛选</span>
            </div>
            <div class="filter-content">
                <el-input v-model="filterForm.keyword" placeholder="输入订单号或用户名" style="width: 300px; margin-right: 20px;"
                    clearable />
                <span class="date-label">日期</span>
                <el-date-picker v-model="filterForm.beginTime" type="datetime" placeholder="开始时间"
                    style="width: 200px; margin-right: 10px;" format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss" />
                <el-date-picker v-model="filterForm.endTime" type="datetime" placeholder="结束时间"
                    style="width: 200px; margin-right: 20px;" format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss" />
                <el-button type="primary" @click="handleFilter">筛选</el-button>
                <el-button type="success" @click="generateTestData">加载测试数据</el-button>
            </div>
        </div>

        <!-- 数据列表区域 -->
        <div class="data-section">
            <div class="data-header">
                <el-icon>
                    <Grid />
                </el-icon>
                <span>数据列表</span>
            </div>

            <!-- 状态标签页 -->
            <div class="status-tabs">
                <el-tabs v-model="activeStatus">
                    <el-tab-pane :label="`全部 (${getStatusCount('all')})`" name="all" />
                    <el-tab-pane :label="`待支付 (${getStatusCount('待支付')})`" name="待支付" />
                    <el-tab-pane :label="`已完成 (${getStatusCount('已完成')})`" name="已完成" />
                    <el-tab-pane :label="`已取消 (${getStatusCount('已取消')})`" name="已取消" />
                    <el-tab-pane :label="`已退款 (${getStatusCount('已退款')})`" name="已退款" />
                </el-tabs>
            </div>

            <!-- 数据表格 -->
            <el-table :data="orderList" :key="tableKey" style="width: 100%" v-loading="loading">
                <el-table-column prop="orderNumber" label="订单编号" width="180" />
                <el-table-column prop="submitTime" label="提交时间" width="180">
                    <template #default="scope">
                        {{ formatDateTime(scope.row.submitTime) }}
                    </template>
                </el-table-column>
                <el-table-column prop="userName" label="用户名" width="150" />
                <el-table-column prop="doctorName" label="医生" width="120" />
                <el-table-column prop="department" label="科室" width="120" />
                <el-table-column prop="medicalServiceFee" label="挂号费" width="100">
                    <template #default="scope">
                        ¥{{ scope.row.medicalServiceFee }}
                    </template>
                </el-table-column>
                <el-table-column prop="orderStatus" label="订单状态" width="120">
                    <template #default="scope">
                        <el-tag :type="getOrderStatusType(scope.row.orderStatus)">
                            {{ getOrderStatusText(scope.row.orderStatus) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                    <template #default="scope">
                        <el-button type="primary" link @click="viewOrderDetail(scope.row)">
                            查看订单
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-wrapper">
                <el-pagination v-model:current-page="pagination.pageIndex" v-model:page-size="pagination.pageSize"
                    :page-sizes="[4, 8, 12, 16]" :total="pagination.total"
                    layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Grid } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import type { RegistrationOrder } from '@/types/order'
import { formatDateTime, getOrderStatusText, getOrderStatusType } from '@/utils/format'
import axios from 'axios'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const activeStatus = ref('all')
const orderList = ref<RegistrationOrder[]>([])
const allOrderList = ref<RegistrationOrder[]>([]) // 存储所有数据，用于客户端筛选
const tableKey = ref(0) // 用于强制刷新表格

// 筛选表单
const filterForm = reactive({
    keyword: '',
    beginTime: '',
    endTime: ''
})

// 分页信息
const pagination = reactive({
    pageIndex: 1,
    pageSize: 4, // 增加默认每页显示数量
    total: 0
})

// 获取订单列表
const getOrderList = async () => {
    loading.value = true
    try {
        // 为了支持客户端筛选，我们需要获取所有数据
        // 使用较大的pageSize来获取更多数据，或者分批获取
        const params = {
            pageIndex: 1, // 总是从第一页开始
            pageSize: 100, // 使用较大的页面大小获取更多数据
            orderNumber: filterForm.keyword || undefined,
            begin: filterForm.beginTime || undefined,
            end: filterForm.endTime || undefined,
            // 不在API层面筛选状态，在客户端筛选
            // status: activeStatus.value !== 'all' ? activeStatus.value : undefined
        }

        console.log('请求参数:', params)
        console.log('API地址:', `${axios.defaults.baseURL}/api/Management/GetRegistrationOrder`)

        const response = await orderApi.getRegistrationOrders(params)

        console.log('API响应:', response)
        console.log('响应数据:', response.data)

        // 适配后端返回的数据格式
        if (response.data && (response.data.success === true || response.data.code === 200)) {
            console.log('成功获取数据:', response.data.data)
            const allData = response.data.data.pageData || []
            allOrderList.value = allData

            // 应用状态筛选
            applyStatusFilter()

            console.log('订单列表:', orderList.value)
            console.log('总数:', pagination.total)

            if (orderList.value.length === 0) {
                ElMessage.info('暂无数据')
            } else {
                ElMessage.success(`成功加载 ${orderList.value.length} 条数据`)
            }
        } else {
            console.error('API返回失败:', response.data)
            ElMessage.error(response.data?.message || '获取订单列表失败')
            orderList.value = []
            allOrderList.value = []
            pagination.total = 0
        }
    } catch (error: any) {
        console.error('获取订单列表失败:', error)
        console.error('错误详情:', {
            message: error?.message,
            response: error?.response,
            request: error?.request
        })

        if (error?.response) {
            // 服务器响应了错误状态码
            ElMessage.error(`服务器错误: ${error.response.status} - ${error.response.statusText}`)
        } else if (error?.request) {
            // 请求已发出但没有收到响应
            ElMessage.error('网络连接失败，请检查后端服务是否启动')
        } else {
            // 其他错误
            ElMessage.error(`请求失败: ${error?.message || '未知错误'}`)
        }

        orderList.value = []
        pagination.total = 0
    } finally {
        loading.value = false
    }
}

// 筛选处理
const handleFilter = () => {
    pagination.pageIndex = 1
    getOrderList()
}

// 获取指定状态的数据数量
const getStatusCount = (status: string) => {
    if (status === 'all') {
        return allOrderList.value.length
    }
    return allOrderList.value.filter(order => order.orderStatus === status).length
}

// 应用状态筛选
const applyStatusFilter = () => {
    console.log('=== 开始应用状态筛选 ===')
    console.log('当前状态:', activeStatus.value)
    console.log('全部数据量:', allOrderList.value.length)
    console.log('全部数据:', allOrderList.value)

    let filteredData: RegistrationOrder[] = []

    if (activeStatus.value === 'all') {
        // 显示所有数据
        filteredData = [...allOrderList.value]
        console.log('显示所有数据')
    } else {
        // 根据状态筛选数据
        filteredData = allOrderList.value.filter(order => {
            console.log(`订单 ${order.orderNumber} 状态: ${order.orderStatus}, 匹配: ${order.orderStatus === activeStatus.value}`)
            return order.orderStatus === activeStatus.value
        })
        console.log('筛选后的数据:', filteredData)
    }

    // 应用客户端分页
    const startIndex = (pagination.pageIndex - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    orderList.value = filteredData.slice(startIndex, endIndex)

    // 更新总数（这是筛选后的总数，不是当前页的数量）
    pagination.total = filteredData.length

    // 如果当前页超出范围，重置到第一页
    if (pagination.pageIndex > Math.ceil(filteredData.length / pagination.pageSize)) {
        pagination.pageIndex = 1
        // 重新计算当前页数据
        const newStartIndex = (pagination.pageIndex - 1) * pagination.pageSize
        const newEndIndex = newStartIndex + pagination.pageSize
        orderList.value = filteredData.slice(newStartIndex, newEndIndex)
    }

    // 强制刷新表格
    tableKey.value++

    console.log(`状态筛选完成: ${activeStatus.value}`)
    console.log(`筛选后总数据量: ${filteredData.length}`)
    console.log(`当前页显示: ${orderList.value.length} 条`)
    console.log(`当前页码: ${pagination.pageIndex}/${Math.ceil(filteredData.length / pagination.pageSize)}`)
    console.log('=== 状态筛选结束 ===')
}

// 状态变化处理
const handleStatusChange = async (tabName: string) => {
    console.log('状态切换到:', tabName)
    console.log('activeStatus.value:', activeStatus.value)

    // 确保activeStatus已更新
    activeStatus.value = tabName
    pagination.pageIndex = 1

    // 等待下一个tick确保状态更新
    await nextTick()

    // 如果有数据，直接应用客户端筛选
    if (allOrderList.value.length > 0) {
        console.log('应用客户端筛选')
        applyStatusFilter()
    } else {
        // 如果没有数据，重新获取
        console.log('重新获取数据')
        getOrderList()
    }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
    pagination.pageSize = size
    pagination.pageIndex = 1

    // 如果有筛选数据，重新应用筛选和分页
    if (allOrderList.value.length > 0) {
        applyStatusFilter()
    } else {
        getOrderList()
    }
}

// 当前页变化
const handleCurrentChange = (page: number) => {
    pagination.pageIndex = page

    // 如果有筛选数据，重新应用筛选和分页
    if (allOrderList.value.length > 0) {
        applyStatusFilter()
    } else {
        getOrderList()
    }
}

// 查看订单详情
const viewOrderDetail = (row: RegistrationOrder) => {
    router.push({
        name: 'RegistrationFan',
        query: { orderNumber: row.orderNumber }
    })
}

// 生成测试数据
const generateTestData = () => {
    const testData: RegistrationOrder[] = [
        {
            orderNumber: 'ORD20241201001',
            orderStatus: '待支付',
            totalAmount: 50,
            actualPayment: 50,
            medicalServiceFee: 50,
            appointmentDateTime: '2024-12-02 09:00:00',
            hospital: '北京协和医院',
            department: '内科',
            doctorName: '张医生',
            doctorTitle: '主任医师',
            registrationNumber: 'REG001',
            patientName: '张三',
            patientGender: '男',
            patientAge: 35,
            patientPhone: '13800138000',
            submitTime: '2024-12-01 10:30:00',
            userName: 'user001',
            createTime: '2024-12-01 10:30:00'
        },
        {
            orderNumber: 'ORD20241201002',
            orderStatus: '已完成',
            totalAmount: 80,
            actualPayment: 80,
            medicalServiceFee: 80,
            appointmentDateTime: '2024-12-02 14:00:00',
            hospital: '北京协和医院',
            department: '外科',
            doctorName: '李医生',
            doctorTitle: '副主任医师',
            registrationNumber: 'REG002',
            patientName: '李四',
            patientGender: '女',
            patientAge: 28,
            patientPhone: '13900139000',
            submitTime: '2024-12-01 11:00:00',
            userName: 'user002',
            createTime: '2024-12-01 11:00:00'
        },
        {
            orderNumber: 'ORD20241201003',
            orderStatus: '待支付',
            totalAmount: 60,
            actualPayment: 60,
            medicalServiceFee: 60,
            appointmentDateTime: '2024-12-03 10:00:00',
            hospital: '北京协和医院',
            department: '儿科',
            doctorName: '王医生',
            doctorTitle: '副主任医师',
            registrationNumber: 'REG003',
            patientName: '王五',
            patientGender: '男',
            patientAge: 8,
            patientPhone: '13700137000',
            submitTime: '2024-12-01 12:00:00',
            userName: 'user003',
            createTime: '2024-12-01 12:00:00'
        },
        {
            orderNumber: 'ORD20241201004',
            orderStatus: '已取消',
            totalAmount: 45,
            actualPayment: 0,
            medicalServiceFee: 45,
            appointmentDateTime: '2024-12-03 15:00:00',
            hospital: '北京协和医院',
            department: '妇科',
            doctorName: '赵医生',
            doctorTitle: '主治医师',
            registrationNumber: 'REG004',
            patientName: '赵六',
            patientGender: '女',
            patientAge: 32,
            patientPhone: '13600136000',
            submitTime: '2024-12-01 13:00:00',
            userName: 'user004',
            createTime: '2024-12-01 13:00:00'
        },
        {
            orderNumber: 'ORD20241201005',
            orderStatus: '已退款',
            totalAmount: 55,
            actualPayment: 55,
            medicalServiceFee: 55,
            appointmentDateTime: '2024-12-04 09:30:00',
            hospital: '北京协和医院',
            department: '眼科',
            doctorName: '孙医生',
            doctorTitle: '主任医师',
            registrationNumber: 'REG005',
            patientName: '孙七',
            patientGender: '男',
            patientAge: 45,
            patientPhone: '13500135000',
            submitTime: '2024-12-01 14:00:00',
            userName: 'user005',
            createTime: '2024-12-01 14:00:00'
        }
    ]

    // 存储所有测试数据
    allOrderList.value = testData

    // 应用当前状态筛选
    applyStatusFilter()

    ElMessage.success(`已加载测试数据，共 ${testData.length} 条`)
}

// 使用工具函数，删除重复的格式化函数

// 监听状态变化
watch(activeStatus, async (newStatus, oldStatus) => {
    console.log(`状态从 ${oldStatus} 变更为 ${newStatus}`)

    // 重置分页
    pagination.pageIndex = 1

    // 等待下一个tick确保状态更新
    await nextTick()

    if (allOrderList.value.length > 0) {
        console.log('watch触发：应用客户端筛选')
        applyStatusFilter()
    } else {
        console.log('watch触发：重新获取数据')
        getOrderList()
    }
})

// 组件挂载时获取数据
onMounted(() => {
    getOrderList()
})

    // 暴露测试数据函数到全局，方便调试
    ; (window as any).generateTestData = generateTestData
</script>

<style scoped>
.registration-management {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header h1 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.filter-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #333;
}

.filter-header .el-icon {
    margin-right: 8px;
    color: #409eff;
}

.filter-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.date-label {
    font-weight: 500;
    color: #666;
    margin-right: 10px;
}

.data-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.data-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-weight: 600;
    color: #333;
}

.data-header .el-icon {
    margin-right: 8px;
    color: #409eff;
}

.status-tabs {
    margin-bottom: 20px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

:deep(.el-tabs__item) {
    font-size: 14px;
    padding: 0 20px;
}

:deep(.el-table) {
    border-radius: 8px;
    overflow: hidden;
}

:deep(.el-table th) {
    background-color: #fafafa;
    color: #333;
    font-weight: 600;
}

:deep(.el-table td) {
    padding: 12px 0;
}

:deep(.el-button--link) {
    padding: 0;
    font-size: 14px;
}
</style>