<script setup lang="ts">
import { ref } from 'vue'
import { RouterView, useRouter } from 'vue-router'
import {
  Document,
  ChatDotRound,
  Notebook,
  ShoppingBag,
  RefreshLeft,
  Menu as MenuIcon,
  Fold,
  Expand
} from '@element-plus/icons-vue'

const router = useRouter()
const isCollapsed = ref(false)

// 菜单数据
const menuItems = [
  {
    title: '挂号单管理',
    icon: Document,
    route: '/RegistrationShow',
    description: '管理医院挂号订单'
  },
  {
    title: '问诊单管理',
    icon: ChatDotRound,
    route: '/ConsultationShow',
    description: '管理在线问诊订单'
  },
  {
    title: '处方流转订单管理',
    icon: Notebook,
    route: '/PrescriptionShow',
    description: '管理处方流转订单'
  },
  {
    title: '药品订单管理',
    icon: ShoppingBag,
    route: '/MedicineShow',
    description: '管理药品购买订单'
  },
  {
    title: '退款申请',
    icon: RefreshLeft,
    route: '/RefundShow',
    description: '管理退款申请订单'
  }
]

// 切换侧边栏折叠状态
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value
}

// 导航到指定路由
const navigateTo = (route: string) => {
  router.push(route)
}
</script>

<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: isCollapsed }">
      <!-- 侧边栏头部 -->
      <div class="sidebar-header">
        <div class="logo" v-show="!isCollapsed">
          <h2>工单管理系统</h2>
        </div>
        <el-button type="text" @click="toggleCollapse" class="collapse-btn">
          <el-icon>
            <Expand v-if="isCollapsed" />
            <Fold v-else />
          </el-icon>
        </el-button>
      </div>

      <!-- 菜单列表 -->
      <nav class="menu-nav">
        <div class="menu-title" v-show="!isCollapsed">
          <el-icon>
            <MenuIcon />
          </el-icon>
          <span>订单管理</span>
        </div>

        <ul class="menu-list">
          <li v-for="item in menuItems" :key="item.route" class="menu-item" @click="navigateTo(item.route)">
            <router-link :to="item.route" class="menu-link">
              <el-icon class="menu-icon">
                <component :is="item.icon" />
              </el-icon>
              <div class="menu-content" v-show="!isCollapsed">
                <span class="menu-text">{{ item.title }}</span>
                <span class="menu-desc">{{ item.description }}</span>
              </div>
            </router-link>
          </li>
        </ul>
      </nav>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content" :class="{ expanded: isCollapsed }">
      <RouterView />
    </main>
  </div>
</template>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 侧边栏样式 */
.sidebar {
  width: 280px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1000;
}

.sidebar.collapsed {
  width: 70px;
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 70px;
}

.logo h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.collapse-btn {
  color: white !important;
  border: none !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 6px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
}

/* 菜单导航 */
.menu-nav {
  padding: 20px 0;
}

.menu-title {
  display: flex;
  align-items: center;
  padding: 0 20px 15px;
  font-size: 14px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

.menu-title .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 菜单列表 */
.menu-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.menu-item {
  margin-bottom: 5px;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  border-radius: 0 25px 25px 0;
  margin-right: 15px;
  position: relative;
}

.menu-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(5px);
}

.menu-link.router-link-active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-link.router-link-active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: #fff;
  border-radius: 0 2px 2px 0;
}

.menu-icon {
  font-size: 20px;
  margin-right: 15px;
  min-width: 20px;
}

.menu-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.menu-text {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 2px;
}

.menu-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.2;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  margin-left: 0;
  transition: margin-left 0.3s ease;
  overflow-y: auto;
  background-color: #f5f5f5;
}

.main-content.expanded {
  margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    width: 70px;
  }

  .sidebar .menu-content {
    display: none;
  }

  .sidebar .logo {
    display: none;
  }

  .menu-title {
    display: none;
  }

  .menu-link {
    justify-content: center;
    padding: 15px 10px;
  }

  .menu-icon {
    margin-right: 0;
  }
}

/* 滚动条样式 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.main-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
