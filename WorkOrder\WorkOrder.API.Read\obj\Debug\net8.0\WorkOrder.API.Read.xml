<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WorkOrder.API.Read</name>
    </assembly>
    <members>
        <member name="T:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand">
            <summary>
            问诊单查询命令
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.Begin">
            <summary>
            开始时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.End">
            <summary>
            结束时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.OrderStatus">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.ConsultationMethod">
            <summary>
            问诊方式
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.PatientName">
            <summary>
            患者姓名（支持模糊查询）
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.PageIndex">
            <summary>
            页码（从1开始）
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand.PageSize">
            <summary>
            每页大小
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.GetConsultationOrderDetailCommand">
            <summary>
            获取问诊单详情命令
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.GetConsultationOrderDetailCommand.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.ConsultationOrderDetailDto">
            <summary>
            问诊单详情数据传输对象
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderDetailDto.ConsultationInfo">
            <summary>
            问诊信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderDetailDto.OrderInfo">
            <summary>
            订单信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderDetailDto.PatientInfo">
            <summary>
            患者信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderDetailDto.DoctorInfo">
            <summary>
            医生信息
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.ConsultationInfo">
            <summary>
            问诊信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.ConsultationMethod">
            <summary>
            问诊方式（视频问诊、电话问诊、图文问诊等）
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.ConsultationSource">
            <summary>
            问诊来源
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.AppointmentTime">
            <summary>
            预约时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.ConsultationStartTime">
            <summary>
            接诊时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.ConsultationEndTime">
            <summary>
            问诊结束时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.CallDurationMinutes">
            <summary>
            通话时长（分钟）
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.SymptomDescription">
            <summary>
            病情描述
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.MedicalHistory">
            <summary>
            就诊情况
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationInfo.HasConsultationRecord">
            <summary>
            是否有问诊记录
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.ConsultationOrderInfo">
            <summary>
            问诊订单信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.OrderType">
            <summary>
            订单类型
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.OrderStatus">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.TotalAmount">
            <summary>
            应付金额
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.CouponAmount">
            <summary>
            优惠券金额
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.ActualPayment">
            <summary>
            实际支付金额
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.ConsultationFee">
            <summary>
            问诊费用
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.CreateTime">
            <summary>
            创建时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.PaymentMethod">
            <summary>
            支付方式
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.PaymentTime">
            <summary>
            支付时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.SubmitTime">
            <summary>
            提交问诊时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.UserName">
            <summary>
            用户名
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationOrderInfo.Remarks">
            <summary>
            备注信息
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.ConsultationPatientInfo">
            <summary>
            问诊患者信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationPatientInfo.PatientName">
            <summary>
            患者姓名
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationPatientInfo.PatientGender">
            <summary>
            患者性别
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationPatientInfo.PatientAge">
            <summary>
            患者年龄
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationPatientInfo.PatientPhone">
            <summary>
            患者手机号
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationPatientInfo.PatientRating">
            <summary>
            患者评分（1-5星）
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.ConsultationPatientInfo.PatientReview">
            <summary>
            患者评价
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.DoctorInfo">
            <summary>
            医生信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.DoctorInfo.DoctorName">
            <summary>
            医生姓名
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.DoctorInfo.Department">
            <summary>
            科室
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.GetOrderDetailCommand">
            <summary>
            获取挂号订单详情命令
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.GetOrderDetailCommand.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.OrderDetailDto">
            <summary>
            订单详情数据传输对象
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderDetailDto.RegistrationInfo">
            <summary>
            挂号信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderDetailDto.OrderInfo">
            <summary>
            订单信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderDetailDto.PatientInfo">
            <summary>
            就诊人信息
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.RegistrationInfo">
            <summary>
            挂号信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.AppointmentDateTime">
            <summary>
            就诊时间
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.Hospital">
            <summary>
            就诊医院
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.Department">
            <summary>
            科室
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.DoctorName">
            <summary>
            医生姓名
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.DoctorTitle">
            <summary>
            医生职称
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.MedicalServiceFee">
            <summary>
            医事服务费
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.RegistrationInfo.RegistrationNumber">
            <summary>
            挂号单号
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.OrderInfo">
            <summary>
            订单信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderInfo.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderInfo.OrderStatus">
            <summary>
            订单状态
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderInfo.TotalAmount">
            <summary>
            应付金额
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderInfo.CouponAmount">
            <summary>
            优惠券金额
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.OrderInfo.ActualPayment">
            <summary>
            实际支付金额
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.command.PatientInfo">
            <summary>
            就诊人信息
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.PatientInfo.PatientName">
            <summary>
            患者姓名
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.PatientInfo.PatientGender">
            <summary>
            患者性别
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.PatientInfo.PatientAge">
            <summary>
            患者年龄
            </summary>
        </member>
        <member name="P:WorkOrder.API.Read.Application.command.PatientInfo.PatientPhone">
            <summary>
            患者联系电话
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.Handler.ConsultationOrderHandler">
            <summary>
            问诊单查询处理器
            </summary>
        </member>
        <member name="M:WorkOrder.API.Read.Application.Handler.ConsultationOrderHandler.GenerateTestData">
            <summary>
            生成测试数据
            </summary>
            <returns></returns>
        </member>
        <member name="T:WorkOrder.API.Read.Application.Handler.GetConsultationOrderDetailHandler">
            <summary>
            获取问诊单详情处理器
            </summary>
        </member>
        <member name="M:WorkOrder.API.Read.Application.Handler.GetConsultationOrderDetailHandler.MapToConsultationOrderDetailDto(WorkOrder.Domain.ConsultationOrder)">
            <summary>
            将问诊订单映射为问诊单详情DTO
            </summary>
        </member>
        <member name="T:WorkOrder.API.Read.Application.Handler.GetOrderDetailHandler">
            <summary>
            获取订单详情处理器
            </summary>
        </member>
        <member name="M:WorkOrder.API.Read.Application.Handler.GetOrderDetailHandler.MapToOrderDetailDto(WorkOrder.Domain.RegistrationOrder)">
            <summary>
            将挂号订单映射为订单详情DTO
            </summary>
        </member>
        <member name="M:WorkOrder.API.Read.Application.Handler.GetOrderDetailHandler.MapToOrderDetailDto(WorkOrder.Domain.ConsultationOrder)">
            <summary>
            将问诊订单映射为订单详情DTO
            </summary>
        </member>
        <member name="M:WorkOrder.API.Read.Controllers.ManagementController.GetRegistrationOrder(WorkOrder.API.Read.Application.command.RegistrationOrderQuerycommand)">
            <summary>
            获取挂号列表
            </summary>
            <param name="request">命令</param>
            <returns>返回任务</returns>
        </member>
        <member name="M:WorkOrder.API.Read.Controllers.ManagementController.GetConsultationOrder(WorkOrder.API.Read.Application.command.ConsultationOrderQueryCommand)">
            <summary>
            获取问诊单列表
            </summary>
            <param name="request">查询命令</param>
            <returns>返回问诊单分页数据</returns>
        </member>
        <member name="M:WorkOrder.API.Read.Controllers.ManagementController.GetConsultationOrderDetail(System.String)">
            <summary>
            获取问诊单详情（用于反填功能）
            </summary>
            <param name="orderNumber">订单编号</param>
            <returns>问诊单详情信息</returns>
        </member>
        <member name="M:WorkOrder.API.Read.Controllers.ManagementController.GetOrderDetail(System.String)">
            <summary>
            获取挂号单详情（用于反填功能）
            </summary>
            <param name="orderNumber">订单编号</param>
            <returns>订单详情信息</returns>
        </member>
    </members>
</doc>
