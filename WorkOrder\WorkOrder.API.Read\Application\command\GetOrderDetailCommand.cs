using MediatR;
using WorkOrder.Domain;
using WorkOrder.ErrorCode;

namespace WorkOrder.API.Read.Application.command
{
    /// <summary>
    /// 获取挂号订单详情命令
    /// </summary>
    public class GetOrderDetailCommand : IRequest<APIResult<OrderDetailDto>>
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// 订单详情数据传输对象
    /// </summary>
    public class OrderDetailDto
    {
        /// <summary>
        /// 挂号信息
        /// </summary>
        public RegistrationInfo RegistrationInfo { get; set; } = new();

        /// <summary>
        /// 订单信息
        /// </summary>
        public OrderInfo OrderInfo { get; set; } = new();

        /// <summary>
        /// 就诊人信息
        /// </summary>
        public PatientInfo PatientInfo { get; set; } = new();
    }

    /// <summary>
    /// 挂号信息
    /// </summary>
    public class RegistrationInfo
    {
        /// <summary>
        /// 就诊时间
        /// </summary>
        public DateTime AppointmentDateTime { get; set; }

        /// <summary>
        /// 就诊医院
        /// </summary>
        public string Hospital { get; set; } = string.Empty;

        /// <summary>
        /// 科室
        /// </summary>
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 医生姓名
        /// </summary>
        public string DoctorName { get; set; } = string.Empty;

        /// <summary>
        /// 医生职称
        /// </summary>
        public string? DoctorTitle { get; set; }

        /// <summary>
        /// 医事服务费
        /// </summary>
        public decimal MedicalServiceFee { get; set; }

        /// <summary>
        /// 挂号单号
        /// </summary>
        public string RegistrationNumber { get; set; } = string.Empty;
    }

    /// <summary>
    /// 订单信息
    /// </summary>
    public class OrderInfo
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单状态
        /// </summary>
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>
        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>
        public decimal ActualPayment { get; set; }
    }

    /// <summary>
    /// 就诊人信息
    /// </summary>
    public class PatientInfo
    {
        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// 患者性别
        /// </summary>
        public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// 患者年龄
        /// </summary>
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者联系电话
        /// </summary>
        public string PatientPhone { get; set; } = string.Empty;
    }
} 