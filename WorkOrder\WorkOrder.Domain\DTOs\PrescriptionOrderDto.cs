using System.ComponentModel.DataAnnotations;

namespace WorkOrder.Domain.DTOs
{
    /// <summary>
    /// 处方流转订单数据传输对象
    /// </summary>
    public class PrescriptionOrderDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 处方编号
        /// </summary>
        public string PrescriptionNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单状态
        /// </summary>
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 处方单状态
        /// </summary>
        public string PrescriptionStatus { get; set; } = string.Empty;

        /// <summary>
        /// 购药方式
        /// </summary>
        public string? PurchaseMethod { get; set; }

        /// <summary>
        /// 发药状态
        /// </summary>
        public string? DispenseStatus { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>
        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>
        public decimal ActualPayment { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// 患者性别
        /// </summary>
        public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// 患者年龄
        /// </summary>
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者手机号
        /// </summary>
        public string PatientPhone { get; set; } = string.Empty;

        /// <summary>
        /// 所患疾病
        /// </summary>
        public string? Disease { get; set; }

        /// <summary>
        /// 肝功能状态
        /// </summary>
        public string? LiverFunction { get; set; }

        /// <summary>
        /// 肾功能状态
        /// </summary>
        public string? KidneyFunction { get; set; }

        /// <summary>
        /// 过敏史
        /// </summary>
        public string? AllergyHistory { get; set; }

        /// <summary>
        /// 生育计划或状态
        /// </summary>
        public string? PregnancyStatus { get; set; }

        /// <summary>
        /// 病情描述
        /// </summary>
        public string? SymptomDescription { get; set; }

        /// <summary>
        /// 已选药品及用药情况
        /// </summary>
        public string? SelectedMedications { get; set; }

        /// <summary>
        /// 处方信息编号
        /// </summary>
        public string PrescriptionInfoNumber { get; set; } = string.Empty;

        /// <summary>
        /// 开单时间
        /// </summary>
        public DateTime PrescriptionDate { get; set; }

        /// <summary>
        /// 处方单状态描述
        /// </summary>
        public string? PrescriptionStatusDescription { get; set; }

        /// <summary>
        /// 医生姓名
        /// </summary>
        public string DoctorName { get; set; } = string.Empty;

        /// <summary>
        /// 科室
        /// </summary>
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 初步诊断
        /// </summary>
        public string? PreliminaryDiagnosis { get; set; }

        /// <summary>
        /// 处理意见
        /// </summary>
        public string? TreatmentAdvice { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 处方药品明细列表
        /// </summary>
        public List<PrescriptionMedicineDto> Medicines { get; set; } = new List<PrescriptionMedicineDto>();
    }

    /// <summary>
    /// 处方药品明细数据传输对象
    /// </summary>
    public class PrescriptionMedicineDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 药品名称
        /// </summary>
        public string MedicineName { get; set; } = string.Empty;

        /// <summary>
        /// 药品规格
        /// </summary>
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 用药说明
        /// </summary>
        public string? Usage { get; set; }

        /// <summary>
        /// 药品类型
        /// </summary>
        public string? MedicineType { get; set; }

        /// <summary>
        /// 是否为处方药
        /// </summary>
        public bool IsPrescriptionDrug { get; set; } = true;
    }

    /// <summary>
    /// 创建处方流转订单请求DTO
    /// </summary>
    public class CreatePrescriptionOrderDto
    {
        /// <summary>
        /// 处方编号
        /// </summary>
        [Required(ErrorMessage = "处方编号不能为空")]
        [StringLength(50, ErrorMessage = "处方编号长度不能超过50个字符")]
        public string PrescriptionNumber { get; set; } = string.Empty;

        /// <summary>
        /// 患者姓名
        /// </summary>
        [Required(ErrorMessage = "患者姓名不能为空")]
        [StringLength(50, ErrorMessage = "患者姓名长度不能超过50个字符")]
        public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// 患者性别
        /// </summary>
        [Required(ErrorMessage = "患者性别不能为空")]
        [RegularExpression("^(男|女)$", ErrorMessage = "患者性别只能是男或女")]
        public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// 患者年龄
        /// </summary>
        [Required(ErrorMessage = "患者年龄不能为空")]
        [Range(0, 150, ErrorMessage = "患者年龄必须在0-150之间")]
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者手机号
        /// </summary>
        [Required(ErrorMessage = "患者手机号不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入正确的手机号码")]
        public string PatientPhone { get; set; } = string.Empty;

        /// <summary>
        /// 医生姓名
        /// </summary>
        [Required(ErrorMessage = "医生姓名不能为空")]
        [StringLength(50, ErrorMessage = "医生姓名长度不能超过50个字符")]
        public string DoctorName { get; set; } = string.Empty;

        /// <summary>
        /// 科室
        /// </summary>
        [Required(ErrorMessage = "科室不能为空")]
        [StringLength(50, ErrorMessage = "科室名称长度不能超过50个字符")]
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 所患疾病
        /// </summary>
        [StringLength(200, ErrorMessage = "疾病描述长度不能超过200个字符")]
        public string? Disease { get; set; }

        /// <summary>
        /// 病情描述
        /// </summary>
        [StringLength(2000, ErrorMessage = "病情描述长度不能超过2000个字符")]
        public string? SymptomDescription { get; set; }

        /// <summary>
        /// 初步诊断
        /// </summary>
        [StringLength(200, ErrorMessage = "初步诊断长度不能超过200个字符")]
        public string? PreliminaryDiagnosis { get; set; }

        /// <summary>
        /// 处理意见
        /// </summary>
        [StringLength(500, ErrorMessage = "处理意见长度不能超过500个字符")]
        public string? TreatmentAdvice { get; set; }

        /// <summary>
        /// 处方药品明细列表
        /// </summary>
        [Required(ErrorMessage = "处方药品明细不能为空")]
        [MinLength(1, ErrorMessage = "至少需要一个药品")]
        public List<CreatePrescriptionMedicineDto> Medicines { get; set; } = new List<CreatePrescriptionMedicineDto>();
    }

    /// <summary>
    /// 创建处方药品明细请求DTO
    /// </summary>
    public class CreatePrescriptionMedicineDto
    {
        /// <summary>
        /// 药品名称
        /// </summary>
        [Required(ErrorMessage = "药品名称不能为空")]
        [StringLength(100, ErrorMessage = "药品名称长度不能超过100个字符")]
        public string MedicineName { get; set; } = string.Empty;

        /// <summary>
        /// 药品规格
        /// </summary>
        [Required(ErrorMessage = "药品规格不能为空")]
        [StringLength(50, ErrorMessage = "药品规格长度不能超过50个字符")]
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        [Required(ErrorMessage = "药品数量不能为空")]
        [Range(1, 999, ErrorMessage = "药品数量必须在1-999之间")]
        public int Quantity { get; set; }

        /// <summary>
        /// 单价
        /// </summary>
        [Required(ErrorMessage = "药品单价不能为空")]
        [Range(0.01, 9999.99, ErrorMessage = "药品单价必须在0.01-9999.99之间")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 用药说明
        /// </summary>
        [StringLength(500, ErrorMessage = "用药说明长度不能超过500个字符")]
        public string? Usage { get; set; }

        /// <summary>
        /// 是否为处方药
        /// </summary>
        public bool IsPrescriptionDrug { get; set; } = true;
    }

    /// <summary>
    /// 处方流转订单查询DTO
    /// </summary>
    public class PrescriptionOrderQueryDto
    {
        /// <summary>
        /// 订单状态
        /// </summary>
        public string? OrderStatus { get; set; }

        /// <summary>
        /// 处方单状态
        /// </summary>
        public string? PrescriptionStatus { get; set; }

        /// <summary>
        /// 购药方式
        /// </summary>
        public string? PurchaseMethod { get; set; }

        /// <summary>
        /// 搜索关键词（患者姓名或手机号）
        /// </summary>
        public string? SearchKeyword { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
}
