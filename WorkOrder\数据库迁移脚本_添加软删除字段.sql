-- 为ConsultationOrder表添加软删除字段
-- 执行前请备份数据库

USE workorder;

-- 检查字段是否已存在，如果不存在则添加
SET @sql = '';

-- 检查IsDeleted字段
SELECT COUNT(*) INTO @count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'workorder' 
  AND TABLE_NAME = 'ConsultationOrder' 
  AND COLUMN_NAME = 'IsDeleted';

IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE ConsultationOrder ADD COLUMN IsDeleted BOOLEAN NOT NULL DEFAULT FALSE;');
END IF;

-- 检查DeletedTime字段
SELECT COUNT(*) INTO @count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'workorder' 
  AND TABLE_NAME = 'ConsultationOrder' 
  AND COLUMN_NAME = 'DeletedTime';

IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE ConsultationOrder ADD COLUMN DeletedTime DATETIME NULL;');
END IF;

-- 检查DeletedBy字段
SELECT COUNT(*) INTO @count 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'workorder' 
  AND TABLE_NAME = 'ConsultationOrder' 
  AND COLUMN_NAME = 'DeletedBy';

IF @count = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE ConsultationOrder ADD COLUMN DeletedBy VARCHAR(100) NULL;');
END IF;

-- 执行SQL
IF LENGTH(@sql) > 0 THEN
    SET @sql = CONCAT(@sql, '');
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    SELECT 'ConsultationOrder表软删除字段添加完成' AS Result;
ELSE
    SELECT 'ConsultationOrder表软删除字段已存在，无需添加' AS Result;
END IF;

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_consultationorder_isdeleted ON ConsultationOrder(IsDeleted);

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'workorder' 
  AND TABLE_NAME = 'ConsultationOrder' 
  AND COLUMN_NAME IN ('IsDeleted', 'DeletedTime', 'DeletedBy')
ORDER BY COLUMN_NAME;
