﻿using MediatR;
using WorkOrder.Domain;
using WorkOrder.ErrorCode;

namespace WorkOrder.API.Read.Application.command
{
    /// <summary>
    /// 问诊单查询命令
    /// </summary>
    public class ConsultationOrderQueryCommand : IRequest<APIResult<APIPageing<ConsultationOrder>>>
    {
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? Begin { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? End { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public string? OrderStatus { get; set; }

        /// <summary>
        /// 问诊方式
        /// </summary>
        public string? ConsultationMethod { get; set; }

        /// <summary>
        /// 患者姓名（支持模糊查询）
        /// </summary>
        public string? PatientName { get; set; }


        /// <summary>
        /// 页码（从1开始）
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 每页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }
}
