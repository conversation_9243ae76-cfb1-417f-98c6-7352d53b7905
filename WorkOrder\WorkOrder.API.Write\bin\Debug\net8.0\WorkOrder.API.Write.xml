<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WorkOrder.API.Write</name>
    </assembly>
    <members>
        <member name="T:WorkOrder.API.Write.Application.command.DeleteConsultationOrderCommand">
            <summary>
            删除问诊单命令
            </summary>
        </member>
        <member name="P:WorkOrder.API.Write.Application.command.DeleteConsultationOrderCommand.OrderNumber">
            <summary>
            订单编号
            </summary>
        </member>
        <member name="T:WorkOrder.API.Write.Application.command.BatchDeleteConsultationOrderCommand">
            <summary>
            批量删除问诊单命令
            </summary>
        </member>
        <member name="P:WorkOrder.API.Write.Application.command.BatchDeleteConsultationOrderCommand.OrderNumbers">
            <summary>
            订单编号列表
            </summary>
        </member>
        <member name="T:WorkOrder.API.Write.Application.Handler.DeleteConsultationOrderHandler">
            <summary>
            删除问诊单处理器（软删除）
            </summary>
        </member>
        <member name="T:WorkOrder.API.Write.Application.Handler.BatchDeleteConsultationOrderHandler">
            <summary>
            批量删除问诊单处理器（软删除）
            </summary>
        </member>
        <member name="M:WorkOrder.API.Write.Controllers.ManagementController.DeleteConsultationOrder(System.String)">
            <summary>
            删除问诊单
            </summary>
            <param name="orderNumber">订单编号</param>
            <returns>删除结果</returns>
        </member>
        <member name="M:WorkOrder.API.Write.Controllers.ManagementController.BatchDeleteConsultationOrder(WorkOrder.API.Write.Application.command.BatchDeleteConsultationOrderCommand)">
            <summary>
            批量删除问诊单
            </summary>
            <param name="command">批量删除命令</param>
            <returns>删除结果</returns>
        </member>
    </members>
</doc>
