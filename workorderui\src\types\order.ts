// 订单状态枚举
export enum OrderStatus {
  PENDING = 'pending',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded'
}

// 问诊单状态枚举
export enum ConsultationStatus {
  PENDING_PAYMENT = '待支付',
  IN_PROGRESS = '进行中',
  COMPLETED = '已完成',
  CANCELLED = '已取消',
  REFUNDED = '已退诊'
}

// 问诊方式枚举
export enum ConsultationMethod {
  VIDEO = '视频问诊',
  PHONE = '电话问诊',
  TEXT = '图文问诊',
  QUICK_PHONE = '极速电话',
  REPEAT_PRESCRIPTION = '复诊开药'
}

// 问诊来源枚举
export enum ConsultationSource {
  VIDEO = '视频问诊',
  PHONE = '电话问诊',
  QUICK_TEXT = '极速图文'
}

// 挂号订单实体
export interface RegistrationOrder {
  orderNumber: string
  orderStatus: string
  totalAmount: number
  couponAmount?: number
  actualPayment: number
  medicalServiceFee: number
  appointmentDateTime: string
  hospital: string
  department: string
  doctorName: string
  doctorTitle?: string
  registrationNumber: string
  patientName: string
  patientGender: string
  patientAge: number
  patientPhone: string
  paymentTime?: string
  submitTime: string
  userName: string
  createTime: string
  paymentMethod?: string
  refundAmount?: number
  refundTime?: string
}

// 订单详情DTO
export interface OrderDetailDto {
  orderNumber: string
  orderStatus: string
  totalAmount: number
  couponAmount?: number
  actualPayment: number
  medicalServiceFee: number
  appointmentDateTime: string
  hospital: string
  department: string
  doctorName: string
  doctorTitle?: string
  registrationNumber: string
  patientName: string
  patientGender: string
  patientAge: number
  patientPhone: string
  paymentTime?: string
  submitTime: string
  userName: string
  createTime: string
  paymentMethod?: string
  refundAmount?: number
  refundTime?: string
}

// 查询参数
export interface OrderQueryParams {
  pageIndex: number
  pageSize: number
  orderNumber?: string
  begin?: string
  end?: string
}

// 分页响应
export interface PaginatedResponse<T> {
  pageData: T[]
  totalCount: number
  pageCount: number
}

// 问诊单实体
export interface ConsultationOrder {
  orderNumber: string
  orderType: string
  consultationMethod: string
  consultationSource: string
  orderStatus: string
  totalAmount: number
  couponAmount?: number
  actualPayment: number
  consultationFee: number
  createTime: string
  paymentMethod?: string
  paymentTime?: string
  submitTime: string
  patientName: string
  patientGender: string
  patientAge: number
  patientPhone: string
  symptomDescription: string
  medicalHistory?: string
  appointmentTime?: string
  submissionTime?: string
  doctorName: string
  department: string
  consultationStartTime?: string
  callDurationMinutes?: number
  patientRating?: number
  patientReview?: string
  consultationEndTime?: string
  hasConsultationRecord: boolean
  userName: string
  remarks?: string
}

// 问诊单查询参数
export interface ConsultationQueryParams {
  pageIndex: number
  pageSize: number
  orderNumber?: string
  begin?: string
  end?: string
  beginTime?: string
  endTime?: string
  orderStatus?: string
  consultationMethod?: string
  patientName?: string
  doctorName?: string
}

// 问诊单详情DTO
export interface ConsultationOrderDetailDto {
  consultationInfo: {
    consultationMethod: string
    consultationSource: string
    appointmentTime?: string
    consultationStartTime?: string
    consultationEndTime?: string
    callDurationMinutes?: number
    symptomDescription: string
    medicalHistory?: string
    hasConsultationRecord: boolean
  }
  orderInfo: {
    orderNumber: string
    orderType: string
    orderStatus: string
    totalAmount: number
    couponAmount?: number
    actualPayment: number
    consultationFee: number
    createTime: string
    paymentMethod?: string
    paymentTime?: string
    submitTime: string
    userName: string
    remarks?: string
  }
  patientInfo: {
    patientName: string
    patientGender: string
    patientAge: number
    patientPhone: string
    patientRating?: number
    patientReview?: string
  }
  doctorInfo: {
    doctorName: string
    department: string
  }
}

// API响应格式
export interface ApiResponse<T> {
  success?: boolean
  code?: number
  message?: string
  data: T
}