{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"DeepSeek/1.0.0": {"dependencies": {"Microsoft.Extensions.Http": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Console": "9.0.7", "Microsoft.SemanticKernel": "1.61.0", "OllamaSharp": "5.3.3", "System.Text.Json": "9.0.7"}, "runtime": {"DeepSeek.dll": {}}}, "Azure.AI.OpenAI/2.2.0-beta.5": {"dependencies": {"Azure.Core": "1.47.0", "OpenAI": "2.2.0"}, "runtime": {"lib/net8.0/Azure.AI.OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "2.200.25.36401"}}}, "Azure.Core/1.47.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "System.ClientModel": "1.5.0", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/Azure.Core.dll": {"assemblyVersion": "1.47.0.0", "fileVersion": "1.4700.25.35905"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.HashCode/1.1.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Bcl.HashCode.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.20.56604"}}}, "Microsoft.Extensions.AI/9.7.1": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.7.1", "Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "System.Diagnostics.DiagnosticSource": "9.0.7", "System.Text.Json": "9.0.7", "System.Threading.Channels": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.dll": {"assemblyVersion": "9.7.0.0", "fileVersion": "9.700.125.36504"}}}, "Microsoft.Extensions.AI.Abstractions/9.7.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.AI.Abstractions.dll": {"assemblyVersion": "9.7.0.0", "fileVersion": "9.700.125.36504"}}}, "Microsoft.Extensions.AI.OpenAI/9.7.1-preview.1.25365.4": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.7.1", "OpenAI": "2.2.0", "System.Memory.Data": "8.0.1", "System.Text.Json": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.AI.OpenAI.dll": {"assemblyVersion": "9.7.0.0", "fileVersion": "9.700.125.36504"}}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Diagnostics/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.7", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "System.Diagnostics.DiagnosticSource": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Http/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Diagnostics": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Http.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "System.Diagnostics.DiagnosticSource": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Configuration/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration": "9.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Logging.Console/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Logging": "9.0.7", "Microsoft.Extensions.Logging.Abstractions": "9.0.7", "Microsoft.Extensions.Logging.Configuration": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "System.Text.Json": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options/9.0.7": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.7": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.7", "Microsoft.Extensions.Configuration.Binder": "9.0.7", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.7", "Microsoft.Extensions.Options": "9.0.7", "Microsoft.Extensions.Primitives": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.Primitives/9.0.7": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.7.1"}, "runtime": {"lib/net8.0/Microsoft.Extensions.VectorData.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.SemanticKernel/1.61.0": {"dependencies": {"Microsoft.SemanticKernel.Connectors.AzureOpenAI": "1.61.0", "Microsoft.SemanticKernel.Core": "1.61.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.dll": {"assemblyVersion": "1.61.0.0", "fileVersion": "1.61.0.0"}}}, "Microsoft.SemanticKernel.Abstractions/1.61.0": {"dependencies": {"Microsoft.Bcl.HashCode": "1.1.1", "Microsoft.Extensions.AI": "9.7.1", "Microsoft.Extensions.VectorData.Abstractions": "9.7.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Abstractions.dll": {"assemblyVersion": "1.61.0.0", "fileVersion": "1.61.0.0"}}}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.61.0": {"dependencies": {"Azure.AI.OpenAI": "2.2.0-beta.5", "Microsoft.SemanticKernel.Connectors.OpenAI": "1.61.0", "Microsoft.SemanticKernel.Core": "1.61.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.AzureOpenAI.dll": {"assemblyVersion": "1.61.0.0", "fileVersion": "1.61.0.0"}}}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.61.0": {"dependencies": {"Microsoft.Extensions.AI.OpenAI": "9.7.1-preview.1.25365.4", "Microsoft.SemanticKernel.Core": "1.61.0", "OpenAI": "2.2.0"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Connectors.OpenAI.dll": {"assemblyVersion": "1.61.0.0", "fileVersion": "1.61.0.0"}}}, "Microsoft.SemanticKernel.Core/1.61.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.7", "Microsoft.SemanticKernel.Abstractions": "1.61.0", "System.Numerics.Tensors": "9.0.7"}, "runtime": {"lib/net8.0/Microsoft.SemanticKernel.Core.dll": {"assemblyVersion": "1.61.0.0", "fileVersion": "1.61.0.0"}}}, "OllamaSharp/5.3.3": {"dependencies": {"Microsoft.Extensions.AI.Abstractions": "9.7.1"}, "runtime": {"lib/net8.0/OllamaSharp.dll": {"assemblyVersion": "5.3.3.0", "fileVersion": "5.3.3.0"}}}, "OpenAI/2.2.0": {"dependencies": {"System.ClientModel": "1.5.0"}, "runtime": {"lib/net8.0/OpenAI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.ClientModel/1.5.0": {"dependencies": {"Microsoft.Extensions.Logging.Abstractions": "9.0.7", "System.Memory.Data": "8.0.1"}, "runtime": {"lib/net8.0/System.ClientModel.dll": {"assemblyVersion": "*******", "fileVersion": "1.500.25.35707"}}}, "System.Diagnostics.DiagnosticSource/9.0.7": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.IO.Pipelines/9.0.7": {"runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Memory.Data/8.0.1": {"runtime": {"lib/net8.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1024.46610"}}}, "System.Numerics.Tensors/9.0.7": {"runtime": {"lib/net8.0/System.Numerics.Tensors.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Text.Encodings.Web/9.0.7": {"runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"rid": "browser", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Text.Json/9.0.7": {"dependencies": {"System.IO.Pipelines": "9.0.7", "System.Text.Encodings.Web": "9.0.7"}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Threading.Channels/8.0.0": {}}}, "libraries": {"DeepSeek/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.AI.OpenAI/2.2.0-beta.5": {"type": "package", "serviceable": true, "sha512": "sha512-TqSsWtlrUwPsBTWSVSFIKN8H7ZM3sdGr0mUqvsQdvy6oQ1SGdRNghHP72pdlKlyqHYzjK6QQDd7lBHFnX1V0HQ==", "path": "azure.ai.openai/2.2.0-beta.5", "hashPath": "azure.ai.openai.2.2.0-beta.5.nupkg.sha512"}, "Azure.Core/1.47.0": {"type": "package", "serviceable": true, "sha512": "sha512-Da6VYrs9qNylfNSURJrytAk4EzBlBv1U62JVww+62CeD+qscD0iK0j5SM1PXhxXylAhH5kx7BuR0yrtmzFmBvA==", "path": "azure.core/1.47.0", "hashPath": "azure.core.1.47.0.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.HashCode/1.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MalY0Y/uM/LjXtHfX/26l2VtN4LDNZ2OE3aumNOHDLsT4fNYy2hiHXI4CXCqKpNUNm7iJ2brrc4J89UdaL56FA==", "path": "microsoft.bcl.hashcode/1.1.1", "hashPath": "microsoft.bcl.hashcode.1.1.1.nupkg.sha512"}, "Microsoft.Extensions.AI/9.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-qAx52ee0Qeb3nWEcdZOrwTab5Uba0JfvtyvjN4+hOx9sincpeVEU6oXizlZyJFn70yz74jRtm19N7CP7EOagHg==", "path": "microsoft.extensions.ai/9.7.1", "hashPath": "microsoft.extensions.ai.9.7.1.nupkg.sha512"}, "Microsoft.Extensions.AI.Abstractions/9.7.1": {"type": "package", "serviceable": true, "sha512": "sha512-fcAMMDxGgdl7vgkiFQ2yojdEwvGIkIpxsv6SQ33enA+q2iGuv3jsFUZmq6ZJ0YvMgrce7mG6GHFkEVcNxjIl0A==", "path": "microsoft.extensions.ai.abstractions/9.7.1", "hashPath": "microsoft.extensions.ai.abstractions.9.7.1.nupkg.sha512"}, "Microsoft.Extensions.AI.OpenAI/9.7.1-preview.1.25365.4": {"type": "package", "serviceable": true, "sha512": "sha512-F2mEShX9r7aril/uEsVUVGBSDkzM/xAo8+Ih2+c4+So43YPnJucieHsHwlqpV+f0bGQm0NnLXl+HTg/f9IEU1g==", "path": "microsoft.extensions.ai.openai/9.7.1-preview.1.25365.4", "hashPath": "microsoft.extensions.ai.openai.9.7.1-preview.1.25365.4.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-oxGR51+w5cXm5B9gU6XwpAB2sTiyPSmZm7hjvv0rzRnmL5o/KZzE103AuQj7sK26OBupjVzU/bZxDWvvU4nhEg==", "path": "microsoft.extensions.configuration/9.0.7", "hashPath": "microsoft.extensions.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-lut/kiVvNsQ120VERMUYSFhpXPpKjjql+giy03LesASPBBcC0o6+aoFdzJH9GaYpFTQ3fGVhVjKjvJDoAW5/IQ==", "path": "microsoft.extensions.configuration.abstractions/9.0.7", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ExY+zXHhU4o9KC2alp3ZdLWyVWVRSn5INqax5ABk+HEOHlAHzomhJ7ek9HHliyOMiVGoYWYaMFOGr9q59mSAGA==", "path": "microsoft.extensions.configuration.binder/9.0.7", "hashPath": "microsoft.extensions.configuration.binder.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i05AYA91vgq0as84ROVCyltD2gnxaba/f1Qw2rG7mUsS0gv8cPTr1Gm7jPQHq7JTr4MJoQUcanLVs16tIOUJaQ==", "path": "microsoft.extensions.dependencyinjection/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-iPK1FxbGFr2Xb+4Y+dTYI8Gupu9pOi8I3JPuPsrogUmEhe2hzZ9LpCmolMEBhVDo2ikcSr7G5zYiwaapHSQTew==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.7", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-6ykfInm6yw7pPHJACgnrPUXxUWVslFnzad44K/siXk6Ovan6fNMnXxI5X9vphHJuZ4JbMOdPIgsfTmLD+Dyxug==", "path": "microsoft.extensions.diagnostics/9.0.7", "hashPath": "microsoft.extensions.diagnostics.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-d39Ov1JpeWCGLCOTinlaDkujhrSAQ0HFxb7Su1BjhCKBfmDcQ6Ia1i3JI6kd3NFgwi1dexTunu82daDNwt7E6w==", "path": "microsoft.extensions.diagnostics.abstractions/9.0.7", "hashPath": "microsoft.extensions.diagnostics.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Http/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-KV2DyFzTyZlrmKBF7IHrg+OhdetkeeByC35vVp50CZogNCbO6c4nzBcjJNnGU0S+CMcrvsN2s8OI5lHwL0wv8A==", "path": "microsoft.extensions.http/9.0.7", "hashPath": "microsoft.extensions.http.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-fdIeQpXYV8yxSWG03cCbU2Otdrq4NWuhnQLXokWLv3L9YcK055E7u8WFJvP+uuP4CFeCEoqZQL4yPcjuXhCZrg==", "path": "microsoft.extensions.logging/9.0.7", "hashPath": "microsoft.extensions.logging.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-sMM6NEAdUTE/elJ2wqjOi0iBWqZmSyaTByLF9e8XHv6DRJFFnOe0N+s8Uc6C91E4SboQCfLswaBIZ+9ZXA98AA==", "path": "microsoft.extensions.logging.abstractions/9.0.7", "hashPath": "microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-AEBty9rvFGvdFRqgIDEhQmiCnIfQWyzVoOZrO244cfu+n9M+wI1QLDpuROVILlplIBtLVmOezAF7d1H3Qog6Xw==", "path": "microsoft.extensions.logging.configuration/9.0.7", "hashPath": "microsoft.extensions.logging.configuration.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-pEHlNa8iCfKsBFA3YVDn/8EicjSU/m8uDfyoR0i4svONDss4Yu9Kznw53E/TyI+TveTo7CwRid4kfd4pLYXBig==", "path": "microsoft.extensions.logging.console/9.0.7", "hashPath": "microsoft.extensions.logging.console.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-trJnF6cRWgR5uMmHpGoHmM1wOVFdIYlELlkO9zX+RfieK0321Y55zrcs4AaEymKup7dxgEN/uJU25CAcMNQRXw==", "path": "microsoft.extensions.options/9.0.7", "hashPath": "microsoft.extensions.options.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-pE/jeAWHEIy/8HsqYA+I1+toTsdvsv+WywAcRoNSvPoFwjOREa8Fqn7D0/i0PbiXsDLFupltTTctliePx8ib4w==", "path": "microsoft.extensions.options.configurationextensions/9.0.7", "hashPath": "microsoft.extensions.options.configurationextensions.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-ti/zD9BuuO50IqlvhWQs9GHxkCmoph5BHjGiWKdg2t6Or8XoyAfRJiKag+uvd/fpASnNklfsB01WpZ4fhAe0VQ==", "path": "microsoft.extensions.primitives/9.0.7", "hashPath": "microsoft.extensions.primitives.9.0.7.nupkg.sha512"}, "Microsoft.Extensions.VectorData.Abstractions/9.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-Vth/omSCX2vR0JabzSRU/hdPhr0CvUVZlaS2lJPWHrEwvak8ntrQLDtLMtMiWKSvviGBe/WmjUW8gA3qqn9tjw==", "path": "microsoft.extensions.vectordata.abstractions/9.7.0", "hashPath": "microsoft.extensions.vectordata.abstractions.9.7.0.nupkg.sha512"}, "Microsoft.SemanticKernel/1.61.0": {"type": "package", "serviceable": true, "sha512": "sha512-f5QC2Q1PNs9y4ORR3qpPG+fSkYvqEfwN4c6hhrUv7JRqwog+hIY3rMhfwVN1FThf6n+6/vhOSh4BB0gBuP+zig==", "path": "microsoft.semantickernel/1.61.0", "hashPath": "microsoft.semantickernel.1.61.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Abstractions/1.61.0": {"type": "package", "serviceable": true, "sha512": "sha512-fgoTP3ndnu2ejdxGw5cgGEa7486YPDlwmIqd3bjH04F8VNQViuLwvkIamrHAS6FWQqJrqXphQOcisWiCNvB2eA==", "path": "microsoft.semantickernel.abstractions/1.61.0", "hashPath": "microsoft.semantickernel.abstractions.1.61.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.AzureOpenAI/1.61.0": {"type": "package", "serviceable": true, "sha512": "sha512-goP0V7rnCf+5S7mWQo9Nx+BuTq9XsdW8NnTLQzG/M11rzBdNXc5Iuo4Q8F6n4rKZ9BUw/zYUOXKBH275IU5OCA==", "path": "microsoft.semantickernel.connectors.azureopenai/1.61.0", "hashPath": "microsoft.semantickernel.connectors.azureopenai.1.61.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Connectors.OpenAI/1.61.0": {"type": "package", "serviceable": true, "sha512": "sha512-PClhCaToycxfwvgYyeFKbMlzHeW679/fFa5qnNLQZlQK5RIqcQZTySY/KtyT+khm7IWz+2IDwrnMpX/IQU01RQ==", "path": "microsoft.semantickernel.connectors.openai/1.61.0", "hashPath": "microsoft.semantickernel.connectors.openai.1.61.0.nupkg.sha512"}, "Microsoft.SemanticKernel.Core/1.61.0": {"type": "package", "serviceable": true, "sha512": "sha512-p+zDwxBl63Ae/LZyWWcJUPs/MFwqi9nf5lSbvrs/gYqKPgs3ig5xOqtDpK7MT8zn2f99s63R5na7zbr+4P/TSw==", "path": "microsoft.semantickernel.core/1.61.0", "hashPath": "microsoft.semantickernel.core.1.61.0.nupkg.sha512"}, "OllamaSharp/5.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-tz5g/BCqeXUOBVEr0pKAWPQZlRMtq/X3O9TE5bGIkIL5Fry0foUY4bElwoMyUKPzZ6bPHICC5C3Dvx9dUwC7uQ==", "path": "ollamasharp/5.3.3", "hashPath": "ollamasharp.5.3.3.nupkg.sha512"}, "OpenAI/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-MSFrQXZsgdwcJq3b8159oA0sYRJk0Io5Rg+MhbNs1NFIhwQ7IlSP2jao7V0NqBqCRfVhvoNxPZeVOqgbOiZ/Nw==", "path": "openai/2.2.0", "hashPath": "openai.2.2.0.nupkg.sha512"}, "System.ClientModel/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyQUynSbLh8IVyETF2tN14IQwBYgif3gIBt2LAL3FSl0W5FIEJIog3sposTxdz23EqDN78LAQFpUxbaj1EsRJw==", "path": "system.clientmodel/1.5.0", "hashPath": "system.clientmodel.1.5.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-i0uHOKl+qabSYMpY8i676wgi8TI9D3yJIh9s8eog9xhcIqWiA+8khfYkAumCmTkJNMsI7qV527VTeSiQc6tPWA==", "path": "system.diagnostics.diagnosticsource/9.0.7", "hashPath": "system.diagnostics.diagnosticsource.9.0.7.nupkg.sha512"}, "System.IO.Pipelines/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-I9KHYFNKQkufs/Ec7evpPPSu2HkuW+jNpq1kT0WOWjzuN6BjxRYy7CuWNLjQmuBzcKd9vKrHaPGcHVxSF5DadQ==", "path": "system.io.pipelines/9.0.7", "hashPath": "system.io.pipelines.9.0.7.nupkg.sha512"}, "System.Memory.Data/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-B<PERSON><PERSON>uec3jV23EMRDeR7Dr1/qhx7369dZzJ9IWy2xylvb4YfXsrUxspWc4UWYid/tj4zZK58uGZqn2WQiaDMhmAg==", "path": "system.memory.data/8.0.1", "hashPath": "system.memory.data.8.0.1.nupkg.sha512"}, "System.Numerics.Tensors/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-/Rpy1cFv8G0ONh8dxmsX0bmWyZ1lrde+T8Occ+N6bDTNFr+E6wWaC/ImI2zW9Mt6Bo2fzuEcRc7J5ke5t97TYQ==", "path": "system.numerics.tensors/9.0.7", "hashPath": "system.numerics.tensors.9.0.7.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-WswuKENaV4gC4ZYZi8BhehJHHRdyZQzXEYv/lV8DHW9FwkdnKaTutdRbK/S1wHZtKUUzzptBPAX2XOxdoURkMw==", "path": "system.text.encodings.web/9.0.7", "hashPath": "system.text.encodings.web.9.0.7.nupkg.sha512"}, "System.Text.Json/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-u/lN2FEEXs3ghj2ta8tWA4r2MS9Yni07K7jDmnz8h1UPDf0lIIIEMkWx383Zz4fJjJio7gDl+00RYuQ/7R8ZQw==", "path": "system.text.json/9.0.7", "hashPath": "system.text.json.9.0.7.nupkg.sha512"}, "System.Threading.Channels/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CMaFr7v+57RW7uZfZkPExsPB6ljwzhjACWW1gfU35Y56rk72B/Wu+sTqxVmGSk4SFUlPc3cjeKND0zktziyjBA==", "path": "system.threading.channels/8.0.0", "hashPath": "system.threading.channels.8.0.0.nupkg.sha512"}}}