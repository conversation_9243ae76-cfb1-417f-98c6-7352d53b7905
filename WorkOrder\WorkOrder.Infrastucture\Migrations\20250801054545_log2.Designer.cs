﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WorkOrder.Infrastucture;

#nullable disable

namespace WorkOrder.Infrastucture.Migrations
{
    [DbContext(typeof(MyDbcontext))]
    [Migration("20250801054545_log2")]
    partial class log2
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.0")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            modelBuilder.Entity("WorkOrder.Domain.ConsultationOrder", b =>
                {
                    b.Property<string>("OrderNumber")
                        .HasColumnType("varchar(95)");

                    b.Property<decimal>("ActualPayment")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime?>("AppointmentTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int?>("CallDurationMinutes")
                        .HasColumnType("int");

                    b.Property<DateTime?>("ConsultationEndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("ConsultationFee")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("ConsultationMethod")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ConsultationSource")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("ConsultationStartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("CouponAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("DoctorName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("HasConsultationRecord")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MedicalHistory")
                        .HasColumnType("longtext");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("OrderType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("PatientAge")
                        .HasColumnType("int");

                    b.Property<string>("PatientGender")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PatientName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PatientPhone")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int?>("PatientRating")
                        .HasColumnType("int");

                    b.Property<string>("PatientReview")
                        .HasColumnType("longtext");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("PaymentTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Remarks")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("SubmissionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SymptomDescription")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("OrderNumber");

                    b.ToTable("ConsultationOrder");
                });

            modelBuilder.Entity("WorkOrder.Domain.MedicineOrder", b =>
                {
                    b.Property<string>("OrderNumber")
                        .HasColumnType("varchar(95)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("LogisticsCompany")
                        .HasColumnType("longtext");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(10,2)");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ReceiverAddress")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ReceiverName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ReceiverPhone")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Remarks")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("TrackingNumber")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("OrderNumber");

                    b.ToTable("MedicineOrder");
                });

            modelBuilder.Entity("WorkOrder.Domain.MedicineOrderItem", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    b.Property<bool>("IsPrescriptionDrug")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("MedicineName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasColumnType("varchar(95)");

                    b.Property<int>("Quantity")
                        .HasColumnType("int");

                    b.Property<string>("Specification")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<decimal>("SubTotal")
                        .HasColumnType("decimal(65,30)");

                    b.Property<decimal>("UnitPrice")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("Usage")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("OrderNumber");

                    b.ToTable("MedicineOrderItems");
                });

            modelBuilder.Entity("WorkOrder.Domain.PermissionModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long>("CreateId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("ModifierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ModifyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("OrderNo")
                        .HasColumnType("int");

                    b.Property<int?>("ParentId")
                        .HasColumnType("int");

                    b.Property<string>("PermissionName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PermissionUrl")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("Permission");
                });

            modelBuilder.Entity("WorkOrder.Domain.PrescriptionOrder", b =>
                {
                    b.Property<string>("OrderNumber")
                        .HasColumnType("varchar(95)");

                    b.Property<decimal>("ActualPayment")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("AllergyHistory")
                        .HasColumnType("longtext");

                    b.Property<decimal?>("CouponAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Disease")
                        .HasColumnType("longtext");

                    b.Property<string>("DispenseStatus")
                        .HasColumnType("longtext");

                    b.Property<string>("DoctorName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsDelete")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("KidneyFunction")
                        .HasColumnType("longtext");

                    b.Property<string>("LiverFunction")
                        .HasColumnType("longtext");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("PatientAge")
                        .HasColumnType("int");

                    b.Property<string>("PatientGender")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PatientName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PatientPhone")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("PaymentTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("PregnancyStatus")
                        .HasColumnType("longtext");

                    b.Property<string>("PreliminaryDiagnosis")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("PrescriptionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("PrescriptionDetails")
                        .HasColumnType("longtext");

                    b.Property<string>("PrescriptionInfoNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PrescriptionStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PrescriptionStatusDescription")
                        .HasColumnType("longtext");

                    b.Property<string>("PurchaseMethod")
                        .HasColumnType("longtext");

                    b.Property<string>("Remarks")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("SelectedMedications")
                        .HasColumnType("longtext");

                    b.Property<string>("SymptomDescription")
                        .HasColumnType("longtext");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("TreatmentAdvice")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("OrderNumber");

                    b.ToTable("PrescriptionOrder");
                });

            modelBuilder.Entity("WorkOrder.Domain.RefundApplication", b =>
                {
                    b.Property<string>("RefundId")
                        .HasColumnType("varchar(95)");

                    b.Property<decimal>("ActualRefundAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("ApplicationStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("ApplicationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("OrderAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("OrderNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ProcessRemarks")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("ProcessTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ProcessorName")
                        .HasColumnType("longtext");

                    b.Property<string>("RefundMethod")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("RefundReason")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("RefundType")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ServiceNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("UpdateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("RefundId");

                    b.ToTable("RefundApplication");
                });

            modelBuilder.Entity("WorkOrder.Domain.RegistrationOrder", b =>
                {
                    b.Property<string>("OrderNumber")
                        .HasColumnType("varchar(95)");

                    b.Property<decimal>("ActualPayment")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("AppointmentDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("CouponAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Department")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("DoctorName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("DoctorTitle")
                        .HasColumnType("longtext");

                    b.Property<string>("Hospital")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<decimal>("MedicalServiceFee")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("OrderStatus")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("PatientAge")
                        .HasColumnType("int");

                    b.Property<string>("PatientGender")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PatientName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PatientPhone")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("PaymentTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal?>("RefundAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<DateTime?>("RefundTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("RegistrationNumber")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("SubmitTime")
                        .HasColumnType("datetime(6)");

                    b.Property<decimal>("TotalAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("OrderNumber");

                    b.ToTable("RegistrationOrder");
                });

            modelBuilder.Entity("WorkOrder.Domain.RoleModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long>("CreateId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("ModifierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ModifyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("RoleName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("RoleState")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("Id");

                    b.ToTable("Role");
                });

            modelBuilder.Entity("WorkOrder.Domain.RolePermissionModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long>("CreateId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("ModifierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ModifyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long>("PermissionId")
                        .HasColumnType("bigint");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("RolePermission");
                });

            modelBuilder.Entity("WorkOrder.Domain.UserModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long>("CreateId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("ModifierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ModifyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("Password")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("UserState")
                        .HasColumnType("tinyint(1)");

                    b.HasKey("Id");

                    b.ToTable("User");
                });

            modelBuilder.Entity("WorkOrder.Domain.UserRoleModel", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    b.Property<long>("CreateId")
                        .HasColumnType("bigint");

                    b.Property<DateTime>("CreateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .HasColumnType("tinyint(1)");

                    b.Property<long?>("ModifierId")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("ModifyTime")
                        .HasColumnType("datetime(6)");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("UserRole");
                });

            modelBuilder.Entity("WorkOrder.Domain.MedicineOrderItem", b =>
                {
                    b.HasOne("WorkOrder.Domain.MedicineOrder", "MedicineOrder")
                        .WithMany("Items")
                        .HasForeignKey("OrderNumber")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MedicineOrder");
                });

            modelBuilder.Entity("WorkOrder.Domain.MedicineOrder", b =>
                {
                    b.Navigation("Items");
                });
#pragma warning restore 612, 618
        }
    }
}
