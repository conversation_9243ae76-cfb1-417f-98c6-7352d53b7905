<template>
    <div class="order-detail">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>订单详情-{{ getOrderStatusText(orderDetail.orderStatus) }}</h1>
            <el-button type="primary" @click="goBack">返回</el-button>
        </div>

        <div v-loading="loading">
            <!-- 挂号信息 -->
            <div class="info-section">
                <div class="section-header">挂号信息</div>
                <div class="section-content">
                    <div class="info-row">
                        <div class="info-item">
                            <label>就诊时间：</label>
                            <span>{{ formatAppointmentTime(orderDetail.appointmentDateTime) }}</span>
                        </div>
                        <div class="info-item">
                            <label>就诊医院：</label>
                            <span>{{ orderDetail.hospital || '北京协和医院' }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <label>科室：</label>
                            <span>{{ orderDetail.department }}</span>
                        </div>
                        <div class="info-item">
                            <label>医生：</label>
                            <span>{{ orderDetail.doctorName }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <label>医生职称：</label>
                            <span>{{ orderDetail.doctorTitle || '主任医师' }}</span>
                        </div>
                        <div class="info-item">
                            <label>医事服务费：</label>
                            <span>¥{{ orderDetail.medicalServiceFee }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <label>挂号单号：</label>
                            <span>{{ orderDetail.registrationNumber || orderDetail.orderNumber }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 订单信息 -->
            <div class="info-section">
                <div class="section-header">订单信息</div>
                <div class="section-content">
                    <div class="info-row">
                        <div class="info-item">
                            <label>订单编号：</label>
                            <span>{{ orderDetail.orderNumber }}</span>
                        </div>
                        <div class="info-item">
                            <label>状态：</label>
                            <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                                {{ getOrderStatusText(orderDetail.orderStatus) }}
                            </el-tag>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <label>应付金额：</label>
                            <span class="amount">¥{{ orderDetail.totalAmount }}</span>
                        </div>
                        <div class="info-item">
                            <label>优惠券：</label>
                            <span class="coupon">¥{{ orderDetail.couponAmount || 0 }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <label>实际支付：</label>
                            <span class="actual-payment">¥{{ orderDetail.actualPayment }}</span>
                        </div>
                        <div class="info-item">
                            <label>支付时间：</label>
                            <span>{{ formatDateTime(orderDetail.paymentTime) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 就诊人信息 -->
            <div class="info-section">
                <div class="section-header">就诊人信息</div>
                <div class="section-content">
                    <div class="info-row">
                        <div class="info-item">
                            <label>姓名：</label>
                            <span>{{ orderDetail.patientName }}</span>
                        </div>
                        <div class="info-item">
                            <label>性别：</label>
                            <span>{{ orderDetail.patientGender }}</span>
                        </div>
                    </div>
                    <div class="info-row">
                        <div class="info-item">
                            <label>年龄：</label>
                            <span>{{ orderDetail.patientAge }}岁</span>
                        </div>
                        <div class="info-item">
                            <label>联系电话：</label>
                            <span>{{ orderDetail.patientPhone }}</span>
                        </div>
                    </div>
                    <!-- 病情描述字段已移除，因为RegistrationOrder实体中没有此字段 -->
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <el-button type="primary" @click="goBack">返回列表</el-button>
                <el-button @click="refreshData">刷新数据</el-button>
                <el-button type="success" @click="generateTestData">加载测试数据</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { orderApi } from '@/api/order'
import type { OrderDetailDto } from '@/types/order'
import { formatDateTime, formatAppointmentTime, getOrderStatusText, getOrderStatusType } from '@/utils/format'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const orderDetail = reactive<OrderDetailDto>({
    orderNumber: '',
    orderStatus: '',
    totalAmount: 0,
    couponAmount: 0,
    actualPayment: 0,
    medicalServiceFee: 0,
    appointmentDateTime: '',
    hospital: '',
    department: '',
    doctorName: '',
    doctorTitle: '',
    registrationNumber: '',
    patientName: '',
    patientGender: '',
    patientAge: 0,
    patientPhone: '',
    // symptomDescription字段已移除
    paymentTime: undefined,
    submitTime: '',
    userName: '',
    createTime: '',
    paymentMethod: ''
})

// 获取订单详情
const getOrderDetail = async () => {
    const orderNumber = route.query.orderNumber as string
    console.log('URL参数中的订单号:', orderNumber)
    console.log('完整的route.query:', route.query)

    if (!orderNumber) {
        ElMessage.error('订单编号不能为空')
        return
    }

    loading.value = true
    try {
        console.log('正在请求订单详情，订单号:', orderNumber)
        const response = await orderApi.getOrderDetail(orderNumber)

        console.log('订单详情API响应:', response)
        console.log('响应数据:', response.data)

        // 适配后端返回的数据格式
        if (response.data && (response.data.success === true || response.data.code === 0)) {
            const data = response.data.data as any // 使用any类型避免类型检查问题
            console.log('订单详情数据:', data)

            // 后端返回的是嵌套结构，需要展平处理
            if (data && data.registrationInfo && data.orderInfo && data.patientInfo) {
                // 合并所有嵌套对象的数据
                const flatData = {
                    // 从 registrationInfo 获取
                    appointmentDateTime: data.registrationInfo.appointmentDateTime,
                    hospital: data.registrationInfo.hospital,
                    department: data.registrationInfo.department,
                    doctorName: data.registrationInfo.doctorName,
                    doctorTitle: data.registrationInfo.doctorTitle,
                    medicalServiceFee: data.registrationInfo.medicalServiceFee,
                    registrationNumber: data.registrationInfo.registrationNumber,

                    // 从 orderInfo 获取
                    orderNumber: data.orderInfo.orderNumber,
                    orderStatus: data.orderInfo.orderStatus,
                    totalAmount: data.orderInfo.totalAmount,
                    couponAmount: data.orderInfo.couponAmount,
                    actualPayment: data.orderInfo.actualPayment,

                    // 从 patientInfo 获取
                    patientName: data.patientInfo.patientName,
                    patientGender: data.patientInfo.patientGender,
                    patientAge: data.patientInfo.patientAge,
                    patientPhone: data.patientInfo.patientPhone,

                    // 其他字段保持原有值或设置默认值
                    paymentTime: orderDetail.paymentTime,
                    submitTime: orderDetail.submitTime,
                    userName: orderDetail.userName,
                    createTime: orderDetail.createTime,
                    paymentMethod: orderDetail.paymentMethod
                }

                console.log('展平后的数据:', flatData)

                // 数据反填
                Object.assign(orderDetail, flatData)
                ElMessage.success('订单详情加载成功')
            } else {
                console.error('数据结构不正确:', data)
                ElMessage.error('数据结构不正确')
            }
        } else {
            console.error('API返回失败:', response.data)
            ElMessage.error(response.data?.message || '获取订单详情失败')
        }
    } catch (error: any) {
        console.error('获取订单详情失败:', error)
        console.error('错误详情:', {
            message: error?.message,
            response: error?.response,
            request: error?.request
        })

        if (error?.response) {
            ElMessage.error(`服务器错误: ${error.response.status} - ${error.response.statusText}`)
        } else if (error?.request) {
            ElMessage.error('网络连接失败，请检查后端服务是否启动')
        } else {
            ElMessage.error(`请求失败: ${error?.message || '未知错误'}`)
        }
    } finally {
        loading.value = false
    }
}

// 生成测试数据
const generateTestData = () => {
    Object.assign(orderDetail, {
        orderNumber: '20250805',
        orderStatus: '待支付',
        totalAmount: 40.00,
        couponAmount: 10.00,
        actualPayment: 40.00,
        medicalServiceFee: 50.00,
        appointmentDateTime: '2025-08-01T09:20:06',
        hospital: '北京市中医院',
        department: '外科',
        doctorName: '光头强',
        doctorTitle: '主任',
        registrationNumber: '202508011043',
        patientName: '肥波',
        patientGender: '男',
        patientAge: 10,
        patientPhone: '13523456677',
        paymentTime: '2025-08-01T09:20:59',
        submitTime: '2025-08-01T09:21:16',
        userName: '张三',
        createTime: '2025-08-01T09:20:52',
        paymentMethod: '微信'
    })
    ElMessage.success('测试数据加载成功')
}

// 返回上一页
const goBack = () => {
    router.back()
}

// 刷新数据
const refreshData = () => {
    getOrderDetail()
}

// 使用工具函数，删除重复的格式化函数

// 组件挂载时获取数据
onMounted(() => {
    getOrderDetail()
})
</script>

<style scoped>
.order-detail {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.page-header h1 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.info-section {
    background: white;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-header {
    background-color: #f8f9fa;
    padding: 15px 20px;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e9ecef;
}

.section-content {
    padding: 20px;
}

.info-row {
    display: flex;
    margin-bottom: 15px;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-item {
    flex: 1;
    display: flex;
    align-items: center;
}

.info-item.full-width {
    flex: 2;
}

.info-item label {
    font-weight: 500;
    color: #666;
    min-width: 100px;
    margin-right: 10px;
}

.info-item span {
    color: #333;
    flex: 1;
}

.amount {
    color: #e6a23c;
    font-weight: 600;
}

.coupon {
    color: #67c23a;
    font-weight: 600;
}

.actual-payment {
    color: #f56c6c;
    font-weight: 600;
    font-size: 16px;
}

.action-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.action-section .el-button {
    margin: 0 10px;
}

:deep(.el-tag) {
    font-weight: 500;
}

:deep(.el-button) {
    border-radius: 6px;
}
</style>
