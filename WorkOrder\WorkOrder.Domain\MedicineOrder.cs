using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WorkOrder.Domain
{
    /// <summary>
    /// 药品订单实体
    /// </summary>
    [Table("MedicineOrder")]
    public class MedicineOrder
    {
        /// <summary>
        /// 药品订单编号（主键）- 格式：MED + 年月日 + 6位序号，如：MED20240101000001
        /// </summary>
        [Key]
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单金额
        /// </summary>
        [Column(TypeName = "decimal(10,2)")]
        public decimal OrderAmount { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
  
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime SubmitTime { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>

        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 收货人姓名
        /// </summary>
        public string ReceiverName { get; set; } = string.Empty;

        /// <summary>
        /// 收货人电话
        /// </summary>
        public string ReceiverPhone { get; set; } = string.Empty;

        /// <summary>
        /// 收货地址
        /// </summary>
        public string ReceiverAddress { get; set; } = string.Empty;

        /// <summary>
        /// 物流公司
        /// </summary>
        public string? LogisticsCompany { get; set; }

        /// <summary>
        /// 物流单号
        /// </summary>
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// 药品明细列表
        /// </summary>
        public virtual ICollection<MedicineOrderItem> Items { get; set; } = new List<MedicineOrderItem>();
    }

    /// <summary>
    /// 药品订单明细实体
    /// </summary>
    [Table("MedicineOrderItems")]
    public class MedicineOrderItem
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// 订单编号（外键）
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 药品名称
        /// </summary>
        public string MedicineName { get; set; } = string.Empty;

        /// <summary>
        /// 药品规格
        /// </summary>

        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 用法用量
        /// </summary>
        public string Usage { get; set; } = string.Empty;

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 小计金额
        /// </summary>

        public decimal SubTotal { get; set; }

        /// <summary>
        /// 是否处方药
        /// </summary>
        public bool IsPrescriptionDrug { get; set; } = true;

        /// <summary>
        /// 导航属性 - 药品订单
        /// </summary>
        [ForeignKey("OrderNumber")]
        public virtual MedicineOrder MedicineOrder { get; set; } = null!;
    }

    /// <summary>
    /// 药品订单状态枚举
    /// </summary>
    public static class MedicineOrderStatus
    {
        /// <summary>
        /// 待发货
        /// </summary>
        public const string PendingShipment = "待发货";

        /// <summary>
        /// 待收货
        /// </summary>
        public const string PendingDelivery = "待收货";

        /// <summary>
        /// 已收货
        /// </summary>
        public const string Delivered = "已收货";

        /// <summary>
        /// 已完成
        /// </summary>
        public const string Completed = "已完成";

        /// <summary>
        /// 已取消
        /// </summary>
        public const string Cancelled = "已取消";

        /// <summary>
        /// 获取所有状态
        /// </summary>
        public static readonly string[] AllStatuses = { PendingShipment, PendingDelivery, Delivered, Completed, Cancelled };
    }
}
