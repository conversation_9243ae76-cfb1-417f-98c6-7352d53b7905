<template>
    <div class="medicine-detail">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>药品订单详情</h1>
        </div>

        <div v-loading="loading" class="detail-container">
            <!-- 药品信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <ShoppingBag />
                    </el-icon>
                    <span>药品信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>药品名称：</label>
                        <span>{{ orderDetail.medicineName }}</span>
                    </div>
                    <div class="info-item">
                        <label>规格：</label>
                        <span>{{ orderDetail.specification }}</span>
                    </div>
                    <div class="info-item">
                        <label>数量：</label>
                        <span>{{ orderDetail.quantity }}</span>
                    </div>
                    <div class="info-item">
                        <label>单价：</label>
                        <span class="price">¥{{ orderDetail.unitPrice }}</span>
                    </div>
                    <div class="info-item">
                        <label>生产厂家：</label>
                        <span>{{ orderDetail.manufacturer }}</span>
                    </div>
                    <div class="info-item">
                        <label>药房：</label>
                        <span>{{ orderDetail.pharmacyName }}</span>
                    </div>
                </div>
            </div>

            <!-- 订单信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <Document />
                    </el-icon>
                    <span>订单信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>订单编号：</label>
                        <span>{{ orderDetail.orderNumber }}</span>
                    </div>
                    <div class="info-item">
                        <label>订单状态：</label>
                        <el-tag :type="getOrderStatusType(orderDetail.orderStatus)">
                            {{ getOrderStatusText(orderDetail.orderStatus) }}
                        </el-tag>
                    </div>
                    <div class="info-item">
                        <label>总金额：</label>
                        <span class="price highlight">¥{{ orderDetail.totalAmount }}</span>
                    </div>
                    <div class="info-item">
                        <label>支付方式：</label>
                        <span>{{ orderDetail.paymentMethod }}</span>
                    </div>
                    <div class="info-item">
                        <label>支付时间：</label>
                        <span>{{ formatDateTime(orderDetail.paymentTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>提交时间：</label>
                        <span>{{ formatDateTime(orderDetail.submitTime) }}</span>
                    </div>
                </div>
            </div>

            <!-- 用户信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <User />
                    </el-icon>
                    <span>用户信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>用户名：</label>
                        <span>{{ orderDetail.userName }}</span>
                    </div>
                    <div class="info-item">
                        <label>联系电话：</label>
                        <span>{{ orderDetail.userPhone }}</span>
                    </div>
                    <div class="info-item">
                        <label>配送地址：</label>
                        <span>{{ orderDetail.deliveryAddress || '无' }}</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <el-button type="primary" @click="goBack">返回列表</el-button>
                <el-button @click="refreshData">刷新数据</el-button>
                <el-button type="success" @click="generateTestData">加载测试数据</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ShoppingBag, Document, User } from '@element-plus/icons-vue'
import { formatDateTime, getOrderStatusText, getOrderStatusType } from '@/utils/format'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const orderDetail = reactive({
    orderNumber: '',
    orderStatus: '',
    totalAmount: 0,
    medicineName: '',
    specification: '',
    quantity: 0,
    unitPrice: 0,
    manufacturer: '',
    pharmacyName: '',
    userName: '',
    userPhone: '',
    deliveryAddress: '',
    paymentTime: '',
    submitTime: '',
    paymentMethod: ''
})

// 获取订单详情
const getOrderDetail = async () => {
    const orderNumber = route.query.orderNumber as string
    if (!orderNumber) {
        ElMessage.error('订单编号不能为空')
        return
    }

    loading.value = true
    try {
        // 这里应该调用药品订单详情的API
        ElMessage.info('药品订单详情功能开发中...')
    } catch (error: any) {
        console.error('获取药品订单详情失败:', error)
        ElMessage.error('获取药品订单详情失败')
    } finally {
        loading.value = false
    }
}

// 生成测试数据
const generateTestData = () => {
    Object.assign(orderDetail, {
        orderNumber: 'MED20241201001',
        orderStatus: 'completed',
        totalAmount: 68.5,
        medicineName: '阿莫西林胶囊',
        specification: '0.25g*24粒',
        quantity: 2,
        unitPrice: 34.25,
        manufacturer: '华北制药',
        pharmacyName: '健康药房',
        userName: 'user001',
        userPhone: '13800138000',
        deliveryAddress: '北京市海淀区某某街道456号',
        paymentTime: '2024-12-01 10:35:00',
        submitTime: '2024-12-01 10:30:00',
        paymentMethod: '微信支付'
    })
    ElMessage.success('测试数据加载成功')
}

// 返回上一页
const goBack = () => {
    router.back()
}

// 刷新数据
const refreshData = () => {
    getOrderDetail()
}

// 组件挂载时获取数据
onMounted(() => {
    getOrderDetail()
})
</script>

<style scoped>
.medicine-detail {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header h1 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.detail-container {
    max-width: 1200px;
    margin: 0 auto;
}

.info-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.section-header .el-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
}

.info-item label {
    font-weight: 600;
    color: #666;
    min-width: 100px;
    margin-right: 10px;
}

.info-item span {
    color: #333;
    flex: 1;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

.price.highlight {
    color: #f56c6c;
    font-size: 16px;
}

.action-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.action-section .el-button {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .info-item label {
        margin-bottom: 5px;
    }
}
</style>