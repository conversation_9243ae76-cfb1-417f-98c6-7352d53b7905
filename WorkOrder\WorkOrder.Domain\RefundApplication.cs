using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WorkOrder.Domain
{
    /// <summary>
    /// 退款申请实体
    /// </summary>
    [Table("RefundApplication")]
    public class RefundApplication
    {
        /// <summary>
        /// 退款申请ID（主键）- 格式：REF + 年月日 + 6位序号，如：REF20240101000001
        /// </summary>
        [Key]
        public string RefundId { get; set; } = string.Empty;

        /// <summary>
        /// 服务编号
        /// </summary>
        public string ServiceNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } 

        /// <summary>
        /// 申请状态（待处理、已处理、已拒绝）
        /// </summary>
        public string ApplicationStatus { get; set; } = "待处理";

        /// <summary>
        /// 申请时间
        /// </summary>
        public DateTime ApplicationTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal OrderAmount { get; set; }

        /// <summary>
        /// 实际退款金额
        /// </summary>

        public decimal ActualRefundAmount { get; set; }

        /// <summary>
        /// 退款方式
        /// </summary>
        public string RefundMethod { get; set; } = string.Empty;

        /// <summary>
        /// 退款类型
        /// </summary>
        public string RefundType { get; set; } = string.Empty;

        /// <summary>
        /// 退款原因
        /// </summary>
        public string RefundReason { get; set; } = string.Empty;

        /// <summary>
        /// 来源（挂号预约、图文回诊、视频回诊、电话回诊）
        /// </summary>
        public string Source { get; set; } = string.Empty;

        /// <summary>
        /// 处理人员
        /// </summary>

        public string? ProcessorName { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public DateTime? ProcessTime { get; set; }

        /// <summary>
        /// 处理备注
        /// </summary>

        public string? ProcessRemarks { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 是否已处理
        /// </summary>

        public bool IsProcessed => ApplicationStatus == "已处理" || ApplicationStatus == "已拒绝";

        /// <summary>
        /// 是否已拒绝
        /// </summary>
  
        public bool IsRejected => ApplicationStatus == "已拒绝";

        /// <summary>
        /// 是否待处理
        /// </summary>

        public bool IsPending => ApplicationStatus == "待处理";
    }

    /// <summary>
    /// 退款申请状态枚举
    /// </summary>
    public static class RefundApplicationStatus
    {
        /// <summary>
        /// 待处理
        /// </summary>
        public const string Pending = "待处理";

        /// <summary>
        /// 已处理
        /// </summary>
        public const string Processed = "已处理";

        /// <summary>
        /// 已拒绝
        /// </summary>
        public const string Rejected = "已拒绝";

        /// <summary>
        /// 获取所有状态
        /// </summary>
        public static readonly string[] AllStatuses = { Pending, Processed, Rejected };
    }

    /// <summary>
    /// 退款来源枚举
    /// </summary>
    public static class RefundSource
    {
        /// <summary>
        /// 挂号预约
        /// </summary>
        public const string Registration = "挂号预约";

        /// <summary>
        /// 图文回诊
        /// </summary>
        public const string TextConsultation = "图文回诊";

        /// <summary>
        /// 视频回诊
        /// </summary>
        public const string VideoConsultation = "视频回诊";

        /// <summary>
        /// 电话回诊
        /// </summary>
        public const string PhoneConsultation = "电话回诊";

        /// <summary>
        /// 获取所有来源
        /// </summary>
        public static readonly string[] AllSources = { Registration, TextConsultation, VideoConsultation, PhoneConsultation };
    }
}
