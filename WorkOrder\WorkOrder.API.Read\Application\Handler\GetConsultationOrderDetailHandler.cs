using MediatR;
using Microsoft.EntityFrameworkCore;
using WorkOrder.API.Read.Application.command;
using WorkOrder.Domain;
using WorkOrder.ErrorCode;
using WorkOrder.Infrastucture;

namespace WorkOrder.API.Read.Application.Handler
{
    /// <summary>
    /// 获取问诊单详情处理器
    /// </summary>
    public class GetConsultationOrderDetailHandler : IRequestHandler<GetConsultationOrderDetailCommand, APIResult<ConsultationOrderDetailDto>>
    {
        private readonly MyDbcontext _context;

        public GetConsultationOrderDetailHandler(MyDbcontext context)
        {
            _context = context;
        }

        public async Task<APIResult<ConsultationOrderDetailDto>> Handle(GetConsultationOrderDetailCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 查询问诊订单（排除已删除的）
                var consultationOrder = await _context.ConsultationOrders
                    .FirstOrDefaultAsync(c => c.OrderNumber == request.OrderNumber && !c.Is<PERSON>eleted, cancellationToken);

                if (consultationOrder == null)
                {
                    return new APIResult<ConsultationOrderDetailDto>
                    {
                        Code = ResultCode.Fail,
                        Message = "未找到指定的问诊单",
                        Data = null
                    };
                }

                return new APIResult<ConsultationOrderDetailDto>
                {
                    Code = ResultCode.Success,
                    Message = "获取问诊单详情成功",
                    Data = MapToConsultationOrderDetailDto(consultationOrder)
                };
            }
            catch (Exception ex)
            {
                return new APIResult<ConsultationOrderDetailDto>
                {
                    Code = ResultCode.Fail,
                    Message = $"获取问诊单详情失败: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// 将问诊订单映射为问诊单详情DTO
        /// </summary>
        private ConsultationOrderDetailDto MapToConsultationOrderDetailDto(ConsultationOrder order)
        {
            return new ConsultationOrderDetailDto
            {
                ConsultationInfo = new ConsultationInfo
                {
                    ConsultationMethod = order.ConsultationMethod,
                    ConsultationSource = order.ConsultationSource,
                    AppointmentTime = order.AppointmentTime,
                    ConsultationStartTime = order.ConsultationStartTime,
                    ConsultationEndTime = order.ConsultationEndTime,
                    CallDurationMinutes = order.CallDurationMinutes,
                    SymptomDescription = order.SymptomDescription,
                    MedicalHistory = order.MedicalHistory,
                    HasConsultationRecord = order.HasConsultationRecord
                },
                OrderInfo = new ConsultationOrderInfo
                {
                    OrderNumber = order.OrderNumber,
                    OrderType = order.OrderType,
                    OrderStatus = order.OrderStatus,
                    TotalAmount = order.TotalAmount,
                    CouponAmount = order.CouponAmount,
                    ActualPayment = order.ActualPayment,
                    ConsultationFee = order.ConsultationFee,
                    CreateTime = order.CreateTime,
                    PaymentMethod = order.PaymentMethod,
                    PaymentTime = order.PaymentTime,
                    SubmitTime = order.SubmitTime,
                    UserName = order.UserName,
                    Remarks = order.Remarks
                },
                PatientInfo = new ConsultationPatientInfo
                {
                    PatientName = order.PatientName,
                    PatientGender = order.PatientGender,
                    PatientAge = order.PatientAge,
                    PatientPhone = order.PatientPhone,
                    PatientRating = order.PatientRating,
                    PatientReview = order.PatientReview
                },
                DoctorInfo = new DoctorInfo
                {
                    DoctorName = order.DoctorName,
                    Department = order.Department
                }
            };
        }
    }
}
