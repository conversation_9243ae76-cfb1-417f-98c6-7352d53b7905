using System.ComponentModel.DataAnnotations;

namespace WorkOrder.Domain.DTOs
{
    /// <summary>
    /// 药品订单数据传输对象
    /// </summary>
    public class MedicineOrderDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单金额
        /// </summary>
        public decimal OrderAmount { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime SubmitTime { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 收货人姓名
        /// </summary>
        public string ReceiverName { get; set; } = string.Empty;

        /// <summary>
        /// 收货人电话
        /// </summary>
        public string ReceiverPhone { get; set; } = string.Empty;

        /// <summary>
        /// 收货地址
        /// </summary>
        public string ReceiverAddress { get; set; } = string.Empty;

        /// <summary>
        /// 物流公司
        /// </summary>
        public string? LogisticsCompany { get; set; }

        /// <summary>
        /// 物流单号
        /// </summary>
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime? UpdateTime { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }

        /// <summary>
        /// 药品明细列表
        /// </summary>
        public List<MedicineOrderItemDto> Items { get; set; } = new List<MedicineOrderItemDto>();
    }

    /// <summary>
    /// 药品订单明细数据传输对象
    /// </summary>
    public class MedicineOrderItemDto
    {
        /// <summary>
        /// 主键ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// 药品名称
        /// </summary>
        public string MedicineName { get; set; } = string.Empty;

        /// <summary>
        /// 药品规格
        /// </summary>
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        public int Quantity { get; set; }

        /// <summary>
        /// 用法用量
        /// </summary>
        public string Usage { get; set; } = string.Empty;

        /// <summary>
        /// 单价
        /// </summary>
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 小计金额
        /// </summary>
        public decimal SubTotal { get; set; }

        /// <summary>
        /// 是否处方药
        /// </summary>
        public bool IsPrescriptionDrug { get; set; } = true;
    }

    /// <summary>
    /// 创建药品订单请求DTO
    /// </summary>
    public class CreateMedicineOrderDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        [Required(ErrorMessage = "用户名不能为空")]
        [StringLength(50, ErrorMessage = "用户名长度不能超过50个字符")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// 收货人姓名
        /// </summary>
        [Required(ErrorMessage = "收货人姓名不能为空")]
        [StringLength(50, ErrorMessage = "收货人姓名长度不能超过50个字符")]
        public string ReceiverName { get; set; } = string.Empty;

        /// <summary>
        /// 收货人电话
        /// </summary>
        [Required(ErrorMessage = "收货人电话不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入正确的手机号码")]
        public string ReceiverPhone { get; set; } = string.Empty;

        /// <summary>
        /// 收货地址
        /// </summary>
        [Required(ErrorMessage = "收货地址不能为空")]
        [StringLength(200, ErrorMessage = "收货地址长度不能超过200个字符")]
        public string ReceiverAddress { get; set; } = string.Empty;

        /// <summary>
        /// 备注
        /// </summary>
        [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
        public string? Remarks { get; set; }

        /// <summary>
        /// 药品明细列表
        /// </summary>
        [Required(ErrorMessage = "药品明细不能为空")]
        [MinLength(1, ErrorMessage = "至少需要一个药品")]
        public List<CreateMedicineOrderItemDto> Items { get; set; } = new List<CreateMedicineOrderItemDto>();
    }

    /// <summary>
    /// 创建药品订单明细请求DTO
    /// </summary>
    public class CreateMedicineOrderItemDto
    {
        /// <summary>
        /// 药品名称
        /// </summary>
        [Required(ErrorMessage = "药品名称不能为空")]
        [StringLength(100, ErrorMessage = "药品名称长度不能超过100个字符")]
        public string MedicineName { get; set; } = string.Empty;

        /// <summary>
        /// 药品规格
        /// </summary>
        [Required(ErrorMessage = "药品规格不能为空")]
        [StringLength(50, ErrorMessage = "药品规格长度不能超过50个字符")]
        public string Specification { get; set; } = string.Empty;

        /// <summary>
        /// 数量
        /// </summary>
        [Required(ErrorMessage = "药品数量不能为空")]
        [Range(1, 999, ErrorMessage = "药品数量必须在1-999之间")]
        public int Quantity { get; set; }

        /// <summary>
        /// 用法用量
        /// </summary>
        [Required(ErrorMessage = "用法用量不能为空")]
        [StringLength(200, ErrorMessage = "用法用量长度不能超过200个字符")]
        public string Usage { get; set; } = string.Empty;

        /// <summary>
        /// 单价
        /// </summary>
        [Required(ErrorMessage = "药品单价不能为空")]
        [Range(0.01, 9999.99, ErrorMessage = "药品单价必须在0.01-9999.99之间")]
        public decimal UnitPrice { get; set; }

        /// <summary>
        /// 是否处方药
        /// </summary>
        public bool IsPrescriptionDrug { get; set; } = true;
    }

    /// <summary>
    /// 药品订单查询DTO
    /// </summary>
    public class MedicineOrderQueryDto
    {
        /// <summary>
        /// 订单状态
        /// </summary>
        public string? OrderStatus { get; set; }

        /// <summary>
        /// 搜索关键词（订单编号或用户名）
        /// </summary>
        public string? SearchKeyword { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime? StartTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime? EndTime { get; set; }

        /// <summary>
        /// 页码
        /// </summary>
        public int PageIndex { get; set; } = 1;

        /// <summary>
        /// 页大小
        /// </summary>
        public int PageSize { get; set; } = 10;
    }

    /// <summary>
    /// 更新订单状态DTO
    /// </summary>
    public class UpdateOrderStatusDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        [Required(ErrorMessage = "订单编号不能为空")]
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 新状态
        /// </summary>
        [Required(ErrorMessage = "订单状态不能为空")]
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 物流公司
        /// </summary>
        public string? LogisticsCompany { get; set; }

        /// <summary>
        /// 物流单号
        /// </summary>
        public string? TrackingNumber { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string? Remarks { get; set; }
    }
}
