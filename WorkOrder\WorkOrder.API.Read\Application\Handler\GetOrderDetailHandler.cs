using MediatR;
using Microsoft.EntityFrameworkCore;
using WorkOrder.API.Read.Application.command;
using WorkOrder.Domain;
using WorkOrder.ErrorCode;
using WorkOrder.Infrastucture;

namespace WorkOrder.API.Read.Application.Handler
{
    /// <summary>
    /// 获取订单详情处理器
    /// </summary>
    public class GetOrderDetailHandler : IRequestHandler<GetOrderDetailCommand, APIResult<OrderDetailDto>>
    {
        private readonly MyDbcontext _context;

        public GetOrderDetailHandler(MyDbcontext context)
        {
            _context = context;
        }

        public async Task<APIResult<OrderDetailDto>> Handle(GetOrderDetailCommand request, CancellationToken cancellationToken)
        {
            try
            {
                // 首先尝试查询挂号订单
                var registrationOrder = await _context.RegistrationOrders
                    .FirstOrDefaultAsync(r => r.OrderNumber == request.OrderNumber, cancellationToken);

                if (registrationOrder != null)
                {
                    return new APIResult<OrderDetailDto>
                    {
                        Message = "获取挂号订单详情成功",
                        Data = MapToOrderDetailDto(registrationOrder)
                    };
                }

                // 如果挂号订单不存在，尝试查询问诊订单
                var consultationOrder = await _context.ConsultationOrders
                    .FirstOrDefaultAsync(c => c.OrderNumber == request.OrderNumber, cancellationToken);

                if (consultationOrder != null)
                {
                    return new APIResult<OrderDetailDto>
                    {
                 
                        Message = "获取问诊订单详情成功",
                        Data = MapToOrderDetailDto(consultationOrder)
                    };
                }

                // 如果都不存在，返回错误
                return new APIResult<OrderDetailDto>
                {
              
                    Message = "未找到指定的订单",
                    Data = null
                };
            }
            catch (Exception ex)
            {
                return new APIResult<OrderDetailDto>
                {
           
                    Message = $"获取订单详情失败: {ex.Message}",
                    Data = null
                };
            }
        }

        /// <summary>
        /// 将挂号订单映射为订单详情DTO
        /// </summary>
        private OrderDetailDto MapToOrderDetailDto(RegistrationOrder order)
        {
            return new OrderDetailDto
            {
                RegistrationInfo = new RegistrationInfo
                {
                    AppointmentDateTime = order.AppointmentDateTime,
                    Hospital = order.Hospital,
                    Department = order.Department,
                    DoctorName = order.DoctorName,
                    DoctorTitle = order.DoctorTitle,
                    MedicalServiceFee = order.MedicalServiceFee,
                    RegistrationNumber = order.RegistrationNumber
                },
                OrderInfo = new OrderInfo
                {
                    OrderNumber = order.OrderNumber,
                    OrderStatus = order.OrderStatus,
                    TotalAmount = order.TotalAmount,
                    CouponAmount = order.CouponAmount,
                    ActualPayment = order.ActualPayment
                },
                PatientInfo = new PatientInfo
                {
                    PatientName = order.PatientName,
                    PatientGender = order.PatientGender,
                    PatientAge = order.PatientAge,
                    PatientPhone = order.PatientPhone
                }
            };
        }

        /// <summary>
        /// 将问诊订单映射为订单详情DTO
        /// </summary>
        private OrderDetailDto MapToOrderDetailDto(ConsultationOrder order)
        {
            return new OrderDetailDto
            {
                RegistrationInfo = new RegistrationInfo
                {
                    AppointmentDateTime = order.AppointmentTime ?? DateTime.Now,
                    Hospital = "在线问诊平台", // 问诊订单没有具体医院
                    Department = order.Department,
                    DoctorName = order.DoctorName,
                    DoctorTitle = null, // 问诊订单可能没有职称信息
                    MedicalServiceFee = order.ConsultationFee,
                    RegistrationNumber = order.OrderNumber // 使用订单号作为挂号单号
                },
                OrderInfo = new OrderInfo
                {
                    OrderNumber = order.OrderNumber,
                    OrderStatus = order.OrderStatus,
                    TotalAmount = order.TotalAmount,
                    CouponAmount = order.CouponAmount,
                    ActualPayment = order.ActualPayment
                },
                PatientInfo = new PatientInfo
                {
                    PatientName = order.PatientName,
                    PatientGender = order.PatientGender,
                    PatientAge = order.PatientAge,
                    PatientPhone = order.PatientPhone
                }
            };
        }
    }
} 