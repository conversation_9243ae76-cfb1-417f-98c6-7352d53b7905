﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WorkOrder.Infrastucture;

namespace WorkOrder.Infrastucture
{

    /// <summary>
    /// 仓储接口类
    /// </summary>
    /// <typeparam name="T">实体基类泛型</typeparam>
    public interface IBaseRepository<T> where T : class
    {
        /// <summary>
        /// EFCore上下文
        /// </summary>
        MyDbcontext Context { get; }

        /// <summary>
        /// 泛型添加方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        Task<int> AddAsync(T t);
        /// <summary>
        /// 泛型修改方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        Task<int> UpdateAsync(T t);

        /// <summary>
        /// 泛型批量修改方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        Task<int> UpdateRangAsync(List<T> t);

        /// <summary>
        /// 泛型修改方法
        /// </summary>
        /// <param name="t">实体</param>
        /// <returns>受影响行数</returns>
        Task<int> DeleteAsync(T t);
        /// <summary>
        /// 泛型查询方法
        /// </summary>
        /// <returns>满足条件的数据</returns>
        IQueryable<T> GetAll();
        /// <summary>
        /// 泛型通过id查单条数据方法
        /// </summary>
        /// <param name="id">主键编号</param>
        /// <returns>单条数据</returns>
        Task<T> GetModel(long id);
    }
}
