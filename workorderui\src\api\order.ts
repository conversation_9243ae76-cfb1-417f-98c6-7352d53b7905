import axios from 'axios'
import type {
  OrderQueryParams,
  RegistrationOrder,
  OrderDetailDto,
  PaginatedResponse,
  ApiResponse,
  ConsultationOrder,
  ConsultationQueryParams,
  ConsultationOrderDetailDto
} from '@/types/order'
import { config } from '@/config'

// 配置axios基础URL
axios.defaults.baseURL = config.apiBaseUrl

// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 订单相关API接口
export const orderApi = {
  // 获取挂号订单列表
  getRegistrationOrders: (params: OrderQueryParams) => {
    return axios.get<ApiResponse<PaginatedResponse<RegistrationOrder>>>('/api/Management/GetRegistrationOrder', { params })
  },

  // 获取订单详情
  getOrderDetail: (orderNumber: string) => {
    return axios.get<ApiResponse<OrderDetailDto>>(`/api/Management/GetOrderDetail/${orderNumber}`)
  }
}

// 问诊单相关API接口
export const consultationApi = {
  // 获取问诊单列表
  getConsultationOrders: (params: ConsultationQueryParams) => {
    return axios.get<ApiResponse<PaginatedResponse<ConsultationOrder>>>('/api/Management/GetConsultationOrder', { params })
  },

  // 获取问诊单详情
  getConsultationOrderDetail: (orderNumber: string) => {
    return axios.get<ApiResponse<ConsultationOrderDetailDto>>(`/api/Management/GetConsultationOrderDetail/${orderNumber}`)
  },

  // 删除问诊单 - 调用Write API
  deleteConsultationOrder: (orderNumber: string) => {
    const writeApiUrl = import.meta.env.VITE_WRITE_API_BASE_URL || 'http://localhost:5203'
    return axios.delete<ApiResponse<boolean>>(`${writeApiUrl}/api/Management/DeleteConsultationOrder/${orderNumber}`)
  },

  // 批量删除问诊单 - 调用Write API
  batchDeleteConsultationOrders: (orderNumbers: string[]) => {
    const writeApiUrl = import.meta.env.VITE_WRITE_API_BASE_URL || 'http://localhost:5203'
    return axios.post<ApiResponse<boolean>>(`${writeApiUrl}/api/Management/BatchDeleteConsultationOrder`, { orderNumbers })
  }
}

// 导出axios实例，以便在其他地方使用
export default axios 