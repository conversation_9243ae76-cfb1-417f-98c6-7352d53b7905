using System.ComponentModel.DataAnnotations;

namespace WorkOrder.Domain.DTOs
{
    /// <summary>
    /// 挂号单数据传输对象
    /// </summary>
    public class RegistrationOrderDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 挂号单号
        /// </summary>
        public string RegistrationNumber { get; set; } = string.Empty;

        /// <summary>
        /// 就诊时间
        /// </summary>
        public DateTime AppointmentDateTime { get; set; }

        /// <summary>
        /// 就诊医院
        /// </summary>
        public string Hospital { get; set; } = string.Empty;

        /// <summary>
        /// 科室
        /// </summary>
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 医生姓名
        /// </summary>
        public string DoctorName { get; set; } = string.Empty;

        /// <summary>
        /// 医生职称
        /// </summary>
        public string? DoctorTitle { get; set; }

        /// <summary>
        /// 医事服务费
        /// </summary>
        public decimal MedicalServiceFee { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>
        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>
        public decimal ActualPayment { get; set; }

        /// <summary>
        /// 订单状态
        /// </summary>
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal? RefundAmount { get; set; }

        /// <summary>
        /// 退款时间
        /// </summary>
        public DateTime? RefundTime { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// 患者性别
        /// </summary>
        public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// 患者年龄
        /// </summary>
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者联系电话
        /// </summary>
        public string PatientPhone { get; set; } = string.Empty;

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime SubmitTime { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }
    }

    /// <summary>
    /// 创建挂号单请求DTO
    /// </summary>
    public class CreateRegistrationOrderDto
    {
        /// <summary>
        /// 就诊时间
        /// </summary>
        [Required(ErrorMessage = "就诊时间不能为空")]
        public DateTime AppointmentDateTime { get; set; }

        /// <summary>
        /// 就诊医院
        /// </summary>
        [Required(ErrorMessage = "就诊医院不能为空")]
        [StringLength(100, ErrorMessage = "医院名称长度不能超过100个字符")]
        public string Hospital { get; set; } = string.Empty;

        /// <summary>
        /// 科室
        /// </summary>
        [Required(ErrorMessage = "科室不能为空")]
        [StringLength(50, ErrorMessage = "科室名称长度不能超过50个字符")]
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 医生姓名
        /// </summary>
        [Required(ErrorMessage = "医生姓名不能为空")]
        [StringLength(50, ErrorMessage = "医生姓名长度不能超过50个字符")]
        public string DoctorName { get; set; } = string.Empty;

        /// <summary>
        /// 医生职称
        /// </summary>
        [StringLength(50, ErrorMessage = "医生职称长度不能超过50个字符")]
        public string? DoctorTitle { get; set; }

        /// <summary>
        /// 医事服务费
        /// </summary>
        [Required(ErrorMessage = "医事服务费不能为空")]
        [Range(0, 9999.99, ErrorMessage = "医事服务费必须在0-9999.99之间")]
        public decimal MedicalServiceFee { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>
        [Range(0, 9999.99, ErrorMessage = "优惠券金额必须在0-9999.99之间")]
        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        [Required(ErrorMessage = "患者姓名不能为空")]
        [StringLength(50, ErrorMessage = "患者姓名长度不能超过50个字符")]
        public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// 患者性别
        /// </summary>
        [Required(ErrorMessage = "患者性别不能为空")]
        [RegularExpression("^(男|女)$", ErrorMessage = "患者性别只能是男或女")]
        public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// 患者年龄
        /// </summary>
        [Required(ErrorMessage = "患者年龄不能为空")]
        [Range(0, 150, ErrorMessage = "患者年龄必须在0-150之间")]
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者联系电话
        /// </summary>
        [Required(ErrorMessage = "患者联系电话不能为空")]
        [RegularExpression(@"^1[3-9]\d{9}$", ErrorMessage = "请输入正确的手机号码")]
        public string PatientPhone { get; set; } = string.Empty;
    }

 
}
