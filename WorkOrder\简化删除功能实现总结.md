# 简化删除功能实现总结

## 概述
按照要求简化了问诊单删除功能，移除了删除人和删除原因字段，并将AutoMapper映射代码统一写在MappingProfiles中。

## 简化内容

### 1. 移除的字段和功能 ✅
- ❌ **删除操作人** (`DeletedBy`) - 已移除
- ❌ **删除原因** (`DeleteReason`) - 已移除
- ❌ **复杂的软删除请求模型** (`SoftDeleteRequest`) - 已移除
- ❌ **独立的映射配置文件** (`ConsultationOrderProfile.cs`) - 已删除

### 2. 保留的核心功能 ✅
- ✅ **软删除标记** (`IsDeleted`) - 保留
- ✅ **删除时间** (`DeletedTime`) - 保留
- ✅ **状态验证** - 只有"已取消"状态可删除
- ✅ **AutoMapper映射** - 简化后保留

## 实现细节

### 1. 简化的命令模型

#### 单个删除命令
```csharp
public class DeleteConsultationOrderCommand : IRequest<APIResult<bool>>
{
    /// <summary>
    /// 订单编号
    /// </summary>
    public string OrderNumber { get; set; } = string.Empty;
}
```

#### 批量删除命令
```csharp
public class BatchDeleteConsultationOrderCommand : IRequest<APIResult<bool>>
{
    /// <summary>
    /// 订单编号列表
    /// </summary>
    public List<string> OrderNumbers { get; set; } = new List<string>();
}
```

### 2. 统一的AutoMapper配置

**位置**: `MappingProfiles.cs`

```csharp
public class MappingProfiles : Profile
{
    public MappingProfiles()
    {
        // 软删除映射 - 将订单号映射为软删除状态
        CreateMap<string, ConsultationOrder>()
            .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => true))
            .ForMember(dest => dest.DeletedTime, opt => opt.MapFrom(src => DateTime.Now));
    }
}
```

**映射逻辑**:
- **输入**: 订单号字符串 (`string`)
- **输出**: 更新的问诊单实体 (`ConsultationOrder`)
- **自动设置**: `IsDeleted = true`, `DeletedTime = DateTime.Now`

### 3. 简化的删除处理器

#### 单个删除处理器
```csharp
public async Task<APIResult<bool>> Handle(DeleteConsultationOrderCommand request, CancellationToken cancellationToken)
{
    // 1. 查找订单（排除已删除）
    var order = await _context.ConsultationOrders
        .FirstOrDefaultAsync(c => c.OrderNumber == request.OrderNumber && !c.IsDeleted, cancellationToken);

    // 2. 验证订单存在
    if (order == null) return Fail("未找到指定的问诊单或该订单已被删除");

    // 3. 验证订单状态
    if (order.OrderStatus == "进行中") return Fail("进行中的问诊单不能删除");

    // 4. 使用AutoMapper进行软删除映射
    _mapper.Map(request.OrderNumber, order);

    // 5. 保存更改
    _context.ConsultationOrders.Update(order);
    await _context.SaveChangesAsync(cancellationToken);

    return Success("删除成功");
}
```

#### 批量删除处理器
```csharp
public async Task<APIResult<bool>> Handle(BatchDeleteConsultationOrderCommand request, CancellationToken cancellationToken)
{
    // 1. 验证输入
    if (!request.OrderNumbers?.Any()) return Fail("请选择要删除的问诊单");

    // 2. 查找订单
    var orders = await _context.ConsultationOrders
        .Where(c => request.OrderNumbers.Contains(c.OrderNumber) && !c.IsDeleted)
        .ToListAsync(cancellationToken);

    // 3. 验证订单存在
    if (!orders.Any()) return Fail("未找到指定的问诊单或订单已被删除");

    // 4. 验证订单状态
    var inProgressOrders = orders.Where(o => o.OrderStatus == "进行中").ToList();
    if (inProgressOrders.Any()) return Fail($"以下进行中的问诊单不能删除: {string.Join(", ", inProgressOrders.Select(o => o.OrderNumber))}");

    // 5. 批量软删除映射
    foreach (var order in orders)
    {
        _mapper.Map(order.OrderNumber, order);
    }

    // 6. 保存更改
    _context.ConsultationOrders.UpdateRange(orders);
    await _context.SaveChangesAsync(cancellationToken);

    return Success($"成功删除 {orders.Count} 个问诊单");
}
```

### 4. 简化的AutoMapper注册

**位置**: `ServiceCollectionExtensions.cs`

```csharp
//注册AutoMapper
builder.Services.AddAutoMapper(typeof(MappingProfiles));
```

**简化说明**:
- 只注册一个Profile类
- 移除了独立的ConsultationOrderProfile
- 所有映射配置集中在MappingProfiles中

## 工作流程

### 单个删除流程
1. **前端调用**: `DELETE /api/Management/DeleteConsultationOrder/{orderNumber}`
2. **命令创建**: `DeleteConsultationOrderCommand { OrderNumber = "CON20240101000001" }`
3. **处理器执行**: 
   - 查找订单
   - 验证状态（只能删除"已取消"状态）
   - AutoMapper映射：`string → ConsultationOrder`
   - 设置 `IsDeleted = true`, `DeletedTime = DateTime.Now`
   - 保存到数据库
4. **返回结果**: 成功/失败消息

### 批量删除流程
1. **前端调用**: `POST /api/Management/BatchDeleteConsultationOrder`
2. **命令创建**: `BatchDeleteConsultationOrderCommand { OrderNumbers = ["order1", "order2"] }`
3. **处理器执行**:
   - 批量查找订单
   - 验证所有订单状态
   - 循环AutoMapper映射
   - 批量保存到数据库
4. **返回结果**: 批量操作结果

## 数据库影响

### 软删除字段
```sql
-- 问诊单表中的软删除字段
IsDeleted BOOLEAN DEFAULT FALSE,     -- 软删除标记
DeletedTime DATETIME NULL,           -- 删除时间
DeletedBy VARCHAR(50) NULL           -- 删除操作人（保留字段但不使用）
```

### 查询影响
所有查询都会自动过滤已删除数据：
```csharp
// Read API查询
var list = _consultationRepository.GetAll().Where(c => !c.IsDeleted);

// 详情查询
var order = await _context.ConsultationOrders
    .FirstOrDefaultAsync(c => c.OrderNumber == orderNumber && !c.IsDeleted);
```

## 前端集成

### API调用保持不变
```typescript
// 单个删除
await consultationApi.deleteConsultationOrder(orderNumber)

// 批量删除
await consultationApi.batchDeleteConsultationOrders(['order1', 'order2'])
```

### 删除按钮条件显示
```vue
<el-button 
    v-if="scope.row.orderStatus === '已取消'" 
    type="danger" 
    link 
    @click="handleDelete(scope.row)">
    删除
</el-button>
```

## 简化对比

### 简化前 vs 简化后

| 项目 | 简化前 | 简化后 |
|------|--------|--------|
| **命令字段** | OrderNumber + DeletedBy + DeleteReason | 只有 OrderNumber |
| **映射模型** | 复杂的 SoftDeleteRequest 类 | 直接使用 string |
| **映射配置** | 独立的 ConsultationOrderProfile.cs | 集中在 MappingProfiles.cs |
| **处理逻辑** | 复杂的字段赋值和原因记录 | 简单的AutoMapper映射 |
| **文件数量** | 多个配置文件 | 单一配置文件 |
| **代码行数** | ~150行 | ~80行 |

### 简化优势
1. **代码更简洁** - 减少了50%的代码量
2. **逻辑更清晰** - 专注于核心的软删除功能
3. **维护更容易** - 所有映射配置集中管理
4. **性能更好** - 减少了不必要的字段处理
5. **测试更简单** - 减少了测试用例的复杂度

## 验证测试

### 功能测试
1. ✅ 单个删除：只能删除"已取消"状态的订单
2. ✅ 批量删除：批量处理多个"已取消"订单
3. ✅ 状态验证：拒绝删除"进行中"状态的订单
4. ✅ 软删除：数据标记为删除但不物理删除
5. ✅ 查询过滤：已删除数据不在查询结果中显示

### AutoMapper测试
1. ✅ 映射注册：MappingProfiles正确注册
2. ✅ 映射执行：string → ConsultationOrder 映射正常
3. ✅ 字段设置：IsDeleted和DeletedTime正确设置

## 总结

✅ **简化完成**:
- 移除了删除人和删除原因字段
- 将AutoMapper配置统一到MappingProfiles中
- 简化了删除处理逻辑
- 保持了核心的软删除功能

✅ **功能保持**:
- 软删除机制完整
- 状态验证正常
- 批量删除支持
- 前端集成无变化

✅ **代码质量**:
- 代码更简洁易维护
- 逻辑更清晰
- 性能更优
- 测试更简单

现在删除功能已经完全简化，只保留核心的软删除功能，使用AutoMapper进行简单高效的映射！
