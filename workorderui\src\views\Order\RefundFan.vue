<template>
    <div class="refund-detail">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>退款申请详情</h1>
        </div>

        <div v-loading="loading" class="detail-container">
            <!-- 退款信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <RefreshLeft />
                    </el-icon>
                    <span>退款信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>退款编号：</label>
                        <span>{{ orderDetail.refundNumber }}</span>
                    </div>
                    <div class="info-item">
                        <label>原订单号：</label>
                        <span>{{ orderDetail.originalOrderNumber }}</span>
                    </div>
                    <div class="info-item">
                        <label>退款状态：</label>
                        <el-tag :type="getRefundStatusType(orderDetail.refundStatus)">
                            {{ getRefundStatusText(orderDetail.refundStatus) }}
                        </el-tag>
                    </div>
                    <div class="info-item">
                        <label>退款金额：</label>
                        <span class="price highlight">¥{{ orderDetail.refundAmount }}</span>
                    </div>
                    <div class="info-item">
                        <label>退款原因：</label>
                        <span>{{ orderDetail.refundReason }}</span>
                    </div>
                    <div class="info-item">
                        <label>申请时间：</label>
                        <span>{{ formatDateTime(orderDetail.submitTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>处理时间：</label>
                        <span>{{ formatDateTime(orderDetail.processTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>退款时间：</label>
                        <span>{{ formatDateTime(orderDetail.refundTime) }}</span>
                    </div>
                </div>
            </div>

            <!-- 原订单信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <Document />
                    </el-icon>
                    <span>原订单信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>订单类型：</label>
                        <span>{{ orderDetail.orderType }}</span>
                    </div>
                    <div class="info-item">
                        <label>订单金额：</label>
                        <span class="price">¥{{ orderDetail.originalAmount }}</span>
                    </div>
                    <div class="info-item">
                        <label>支付方式：</label>
                        <span>{{ orderDetail.paymentMethod }}</span>
                    </div>
                    <div class="info-item">
                        <label>支付时间：</label>
                        <span>{{ formatDateTime(orderDetail.paymentTime) }}</span>
                    </div>
                </div>
            </div>

            <!-- 用户信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <User />
                    </el-icon>
                    <span>用户信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>用户名：</label>
                        <span>{{ orderDetail.userName }}</span>
                    </div>
                    <div class="info-item">
                        <label>联系电话：</label>
                        <span>{{ orderDetail.userPhone }}</span>
                    </div>
                    <div class="info-item">
                        <label>退款说明：</label>
                        <span>{{ orderDetail.refundDescription || '无' }}</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <el-button type="primary" @click="goBack">返回列表</el-button>
                <el-button @click="refreshData">刷新数据</el-button>
                <el-button type="success" @click="generateTestData">加载测试数据</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { RefreshLeft, Document, User } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const orderDetail = reactive({
    refundNumber: '',
    originalOrderNumber: '',
    refundStatus: '',
    refundAmount: 0,
    refundReason: '',
    refundDescription: '',
    orderType: '',
    originalAmount: 0,
    userName: '',
    userPhone: '',
    submitTime: '',
    processTime: '',
    refundTime: '',
    paymentTime: '',
    paymentMethod: ''
})

// 退款状态文本映射
const getRefundStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
        'pending': '待审核',
        'approved': '已通过',
        'rejected': '已拒绝',
        'refunded': '已退款'
    }
    return statusMap[status] || status
}

// 退款状态类型映射
const getRefundStatusType = (status: string) => {
    const typeMap: Record<string, string> = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'refunded': 'info'
    }
    return typeMap[status] || 'info'
}

// 获取退款详情
const getOrderDetail = async () => {
    const refundNumber = route.query.refundNumber as string
    if (!refundNumber) {
        ElMessage.error('退款编号不能为空')
        return
    }

    loading.value = true
    try {
        // 这里应该调用退款申请详情的API
        ElMessage.info('退款申请详情功能开发中...')
    } catch (error: any) {
        console.error('获取退款申请详情失败:', error)
        ElMessage.error('获取退款申请详情失败')
    } finally {
        loading.value = false
    }
}

// 生成测试数据
const generateTestData = () => {
    Object.assign(orderDetail, {
        refundNumber: 'REF20241201001',
        originalOrderNumber: 'ORD20241201001',
        refundStatus: 'approved',
        refundAmount: 50.0,
        refundReason: '医生临时有事，无法就诊',
        refundDescription: '由于医生临时有紧急手术，无法按时进行预约的挂号服务，申请全额退款。',
        orderType: '挂号订单',
        originalAmount: 50.0,
        userName: 'user001',
        userPhone: '13800138000',
        submitTime: '2024-12-01 10:30:00',
        processTime: '2024-12-01 14:20:00',
        refundTime: '2024-12-01 15:00:00',
        paymentTime: '2024-12-01 09:35:00',
        paymentMethod: '微信支付'
    })
    ElMessage.success('测试数据加载成功')
}

// 返回上一页
const goBack = () => {
    router.back()
}

// 刷新数据
const refreshData = () => {
    getOrderDetail()
}

// 组件挂载时获取数据
onMounted(() => {
    getOrderDetail()
})
</script>

<style scoped>
.refund-detail {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header h1 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.detail-container {
    max-width: 1200px;
    margin: 0 auto;
}

.info-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.section-header .el-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
}

.info-item label {
    font-weight: 600;
    color: #666;
    min-width: 100px;
    margin-right: 10px;
}

.info-item span {
    color: #333;
    flex: 1;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

.price.highlight {
    color: #f56c6c;
    font-size: 16px;
}

.action-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.action-section .el-button {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .info-item label {
        margin-bottom: 5px;
    }
}
</style>