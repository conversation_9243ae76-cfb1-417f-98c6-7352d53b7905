﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WorkOrder.API.Write.Application.command;
using WorkOrder.ErrorCode;

namespace WorkOrder.API.Write.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ManagementController : ControllerBase
    {
        private readonly IMediator _mediator;

        public ManagementController(IMediator mediator)
        {
            _mediator = mediator;
        }

        /// <summary>
        /// 删除问诊单
        /// </summary>
        /// <param name="orderNumber">订单编号</param>
        /// <returns>删除结果</returns>
        [HttpDelete("{orderNumber}")]
        public Task<APIResult<bool>> DeleteConsultationOrder(string orderNumber)
        {
            var command = new DeleteConsultationOrderCommand { OrderNumber = orderNumber };
            return _mediator.Send(command);
        }

        /// <summary>
        /// 批量删除问诊单
        /// </summary>
        /// <param name="command">批量删除命令</param>
        /// <returns>删除结果</returns>
        [HttpPost]
        public Task<APIResult<bool>> BatchDeleteConsultationOrder([FromBody] BatchDeleteConsultationOrderCommand command)
        {
            return _mediator.Send(command);
        }
    }
}
