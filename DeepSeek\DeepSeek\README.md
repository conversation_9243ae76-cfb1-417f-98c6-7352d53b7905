# DeepSeek RAG 系统

这是一个基于 DeepSeek API 的检索增强生成（RAG）系统，能够基于文档库回答用户问题。

## 功能特性

- 📄 **文档处理**: 支持文本文档的加载、分块和向量化
- 🔍 **智能检索**: 基于语义相似度的文档检索
- 🤖 **AI生成**: 集成 DeepSeek API 进行智能回答生成
- 💾 **向量存储**: 内存向量数据库，支持高效相似度搜索
- 🎯 **RAG流程**: 完整的检索-增强-生成工作流

## 系统架构

```
用户问题 → 向量化 → 相似度检索 → 上下文构建 → DeepSeek生成 → 回答
```

## 快速开始

### 1. 环境准备

确保已安装 .NET 8.0 SDK。

### 2. 配置 API 密钥

设置 DeepSeek API 密钥环境变量：

```bash
# Windows
set DEEPSEEK_API_KEY=your_actual_api_key_here

# Linux/Mac
export DEEPSEEK_API_KEY=your_actual_api_key_here
```

或者直接在代码中修改 `apiKey` 变量。

### 3. 运行程序

```bash
cd DeepSeek
dotnet run
```

### 4. 使用系统

程序启动后会：
1. 加载示例文档
2. 进行文档分块处理
3. 进入交互式问答模式

输入问题即可获得基于文档的智能回答。

## 核心组件

### DocumentProcessor
- 文档加载和预处理
- 智能分块（支持重叠）
- 向量嵌入生成

### VectorStore
- 内存向量数据库
- 余弦相似度计算
- 高效相似度搜索

### RAGRetriever
- 查询向量化
- 相关文档检索
- 上下文格式化

### DeepSeekClient
- DeepSeek API 集成
- 智能回答生成
- 错误处理和重试

### RAGGenerator
- 端到端 RAG 流程
- 检索和生成协调
- 结果优化

## 配置说明

### 文档分块参数
- `chunkSize`: 每个文档块的最大字符数（默认500）
- `overlap`: 块之间的重叠字符数（默认50）

### 检索参数
- `topK`: 返回的最相关文档数量（默认5）
- `threshold`: 相似度阈值（默认0.7）

### 生成参数
- `temperature`: 生成随机性（默认0.7）
- `max_tokens`: 最大生成长度（默认2000）

## 扩展功能

### 支持更多文档格式
可以扩展 `DocumentProcessor` 来支持：
- PDF 文件
- Word 文档
- HTML 页面
- Markdown 文件

### 持久化存储
可以将 `VectorStore` 扩展为：
- 文件存储
- 数据库存储
- 云存储

### 高级检索
可以实现：
- 混合检索（关键词+语义）
- 重排序算法
- 查询扩展

## 注意事项

1. **API 密钥**: 确保正确配置 DeepSeek API 密钥
2. **嵌入服务**: 当前版本需要配置嵌入服务才能完整运行
3. **内存使用**: 大量文档可能消耗较多内存
4. **API 限制**: 注意 DeepSeek API 的调用频率限制

## 故障排除

### 常见问题

**Q: API 调用失败**
A: 检查 API 密钥是否正确，网络连接是否正常

**Q: 嵌入服务未配置**
A: 需要配置 Semantic Kernel 的嵌入服务

**Q: 内存不足**
A: 减少文档数量或调整分块大小

## 许可证

MIT License
