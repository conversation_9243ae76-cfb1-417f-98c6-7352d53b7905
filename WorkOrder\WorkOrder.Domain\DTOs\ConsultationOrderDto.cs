using System.ComponentModel.DataAnnotations;

namespace WorkOrder.Domain.DTOs
{
    /// <summary>
    /// 问诊单数据传输对象
    /// </summary>
    public class ConsultationOrderDto
    {
        /// <summary>
        /// 订单编号
        /// </summary>
        public string OrderNumber { get; set; } = string.Empty;

        /// <summary>
        /// 订单类型
        /// </summary>
        public string OrderType { get; set; } = string.Empty;

        /// <summary>
        /// 问诊方式
        /// </summary>
        public string ConsultationMethod { get; set; } = string.Empty;

        /// <summary>
        /// 问诊来源
        /// </summary>
        public string ConsultationSource { get; set; } = string.Empty;

        /// <summary>
        /// 订单状态
        /// </summary>
        public string OrderStatus { get; set; } = string.Empty;

        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>
        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>
        public decimal ActualPayment { get; set; }

        /// <summary>
        /// 问诊费用
        /// </summary>
        public decimal ConsultationFee { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 提交问诊时间
        /// </summary>
        public DateTime SubmitTime { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public string PatientName { get; set; } = string.Empty;

        /// <summary>
        /// 患者性别
        /// </summary>
        public string PatientGender { get; set; } = string.Empty;

        /// <summary>
        /// 患者年龄
        /// </summary>
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者手机号
        /// </summary>
        public string PatientPhone { get; set; } = string.Empty;

        /// <summary>
        /// 病情描述
        /// </summary>
        public string SymptomDescription { get; set; } = string.Empty;

        /// <summary>
        /// 就诊情况
        /// </summary>
        public string? MedicalHistory { get; set; }

        /// <summary>
        /// 预约时间
        /// </summary>
        public DateTime? AppointmentTime { get; set; }

        /// <summary>
        /// 提交时间
        /// </summary>
        public DateTime? SubmissionTime { get; set; }

        /// <summary>
        /// 医生姓名
        /// </summary>
        public string DoctorName { get; set; } = string.Empty;

        /// <summary>
        /// 科室
        /// </summary>
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 接诊时间
        /// </summary>
        public DateTime? ConsultationStartTime { get; set; }

        /// <summary>
        /// 通话时长（分钟）
        /// </summary>
        public int? CallDurationMinutes { get; set; }

        /// <summary>
        /// 患者评分
        /// </summary>
        public int? PatientRating { get; set; }

        /// <summary>
        /// 患者评价
        /// </summary>
        public string? PatientReview { get; set; }

        /// <summary>
        /// 问诊结束时间
        /// </summary>
        public DateTime? ConsultationEndTime { get; set; }

        /// <summary>
        /// 是否有问诊记录
        /// </summary>
        public bool HasConsultationRecord { get; set; }

        /// <summary>
        /// 用户名
        /// </summary>
        public string? UserName { get; set; }

        /// <summary>
        /// 备注信息
        /// </summary>
        public string? Remarks { get; set; }
    }



}
