# 问诊单软删除功能实现总结

## 概述
将问诊单删除功能改为软删除，并使用AutoMapper实现数据映射，确保数据安全性和可恢复性。

## 已实现的功能

### 1. 软删除实体字段扩展 ✅

**位置**: `WorkOrder.Domain.ConsultationOrder`

**新增字段**:
```csharp
/// <summary>
/// 是否已删除（软删除标记）
/// </summary>
public bool IsDeleted { get; set; } = false;

/// <summary>
/// 删除时间
/// </summary>
public DateTime? DeletedTime { get; set; }

/// <summary>
/// 删除操作人
/// </summary>
public string? DeletedBy { get; set; }
```

**特性**:
- `IsDeleted`: 软删除标记，默认为false
- `DeletedTime`: 记录删除的具体时间
- `DeletedBy`: 记录执行删除操作的用户

### 2. AutoMapper配置文件 ✅

**位置**: `WorkOrder.API.Write.Application.Mapping.ConsultationOrderProfile`

**映射配置**:
```csharp
public class ConsultationOrderProfile : Profile
{
    public ConsultationOrderProfile()
    {
        // 软删除更新映射
        CreateMap<SoftDeleteRequest, ConsultationOrder>()
            .ForMember(dest => dest.IsDeleted, opt => opt.MapFrom(src => true))
            .ForMember(dest => dest.DeletedTime, opt => opt.MapFrom(src => src.DeletedTime ?? DateTime.Now))
            .ForMember(dest => dest.DeletedBy, opt => opt.MapFrom(src => src.DeletedBy ?? "System"));
    }
}
```

**软删除请求模型**:
```csharp
public class SoftDeleteRequest
{
    public string OrderNumber { get; set; } = string.Empty;
    public DateTime? DeletedTime { get; set; }
    public string? DeletedBy { get; set; }
}
```

### 3. 删除命令扩展 ✅

**位置**: `WorkOrder.API.Write.Application.command.ConsultationOrderCommands`

**单个删除命令**:
```csharp
public class DeleteConsultationOrderCommand : IRequest<APIResult<bool>>
{
    public string OrderNumber { get; set; } = string.Empty;
    public string? DeletedBy { get; set; }
    public string? DeleteReason { get; set; }
}
```

**批量删除命令**:
```csharp
public class BatchDeleteConsultationOrderCommand : IRequest<APIResult<bool>>
{
    public List<string> OrderNumbers { get; set; } = new List<string>();
    public string? DeletedBy { get; set; }
    public string? DeleteReason { get; set; }
}
```

### 4. 软删除处理器实现 ✅

**位置**: `WorkOrder.API.Write.Application.Handler.ConsultationOrderHandler`

#### 单个删除处理器
```csharp
public class DeleteConsultationOrderHandler : IRequestHandler<DeleteConsultationOrderCommand, APIResult<bool>>
{
    private readonly MyDbcontext _context;
    private readonly IMapper _mapper;

    // 使用AutoMapper进行软删除映射
    var softDeleteRequest = new SoftDeleteRequest
    {
        OrderNumber = request.OrderNumber,
        DeletedTime = DateTime.Now,
        DeletedBy = request.DeletedBy ?? "System"
    };

    _mapper.Map(softDeleteRequest, order);
    
    // 记录删除原因
    if (!string.IsNullOrEmpty(request.DeleteReason))
    {
        order.Remarks = string.IsNullOrEmpty(order.Remarks) 
            ? $"删除原因: {request.DeleteReason}" 
            : $"{order.Remarks}; 删除原因: {request.DeleteReason}";
    }

    _context.ConsultationOrders.Update(order);
    await _context.SaveChangesAsync(cancellationToken);
}
```

#### 批量删除处理器
- 支持批量软删除多个订单
- 使用AutoMapper进行批量映射
- 统一的删除时间和操作人
- 批量更新数据库

### 5. 查询逻辑修改 ✅

**Read API查询过滤**:
```csharp
// 列表查询 - 过滤已删除数据
var list = _consultationRepository.GetAll().Where(c => !c.IsDeleted);

// 详情查询 - 过滤已删除数据
var consultationOrder = await _context.ConsultationOrders
    .FirstOrDefaultAsync(c => c.OrderNumber == request.OrderNumber && !c.IsDeleted, cancellationToken);
```

### 6. 前端删除按钮条件显示 ✅

**位置**: `workorderui\src\views\Order\ConsultationShow.vue`

**条件显示逻辑**:
```vue
<el-button 
    v-if="scope.row.orderStatus === '已取消'" 
    type="danger" 
    link 
    @click="handleDelete(scope.row)"
    style="margin-left: 10px;">
    删除
</el-button>
```

**删除确认对话框**:
```typescript
const handleDelete = async (row: ConsultationOrder) => {
    await ElMessageBox.confirm(
        `确定要删除订单号为 "${row.orderNumber}" 的问诊单吗？`,
        '删除确认',
        {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning',
            confirmButtonClass: 'el-button--danger'
        }
    )
    
    // 调用删除API
    const response = await consultationApi.deleteConsultationOrder(row.orderNumber)
}
```

## 技术实现特点

### 1. 软删除优势
- **数据安全**: 数据不会真正删除，可以恢复
- **审计追踪**: 记录删除时间和操作人
- **业务连续性**: 相关数据关联不会断裂

### 2. AutoMapper优势
- **类型安全**: 编译时检查映射配置
- **代码简洁**: 减少手动映射代码
- **性能优化**: 自动生成高效的映射代码
- **可维护性**: 集中管理映射配置

### 3. 业务规则
- **状态限制**: 只有"已取消"状态的订单才能删除
- **权限控制**: 可以记录删除操作人
- **原因记录**: 支持记录删除原因到备注字段

### 4. 前端交互
- **条件显示**: 删除按钮只在符合条件时显示
- **确认对话框**: 防止误删操作
- **即时反馈**: 删除后立即刷新列表

## 数据库影响

### 1. 字段变更
- 新增 `IsDeleted` 字段（布尔型，默认false）
- 新增 `DeletedTime` 字段（可空日期时间）
- 新增 `DeletedBy` 字段（可空字符串）

### 2. 查询影响
- 所有查询都需要添加 `WHERE IsDeleted = false` 条件
- 确保软删除的数据不会在正常业务中显示

### 3. 性能考虑
- 建议在 `IsDeleted` 字段上创建索引
- 定期清理长期软删除的数据

## 配置和部署

### 1. AutoMapper注册
```csharp
builder.Services.AddAutoMapper(cfg => {
    cfg.AddProfile<MappingProfiles>();
    cfg.AddProfile<Application.Mapping.ConsultationOrderProfile>();
});
```

### 2. 依赖包
- `AutoMapper` (13.0.1)
- `AutoMapper.Extensions.Microsoft.DependencyInjection` (12.0.1)

### 3. API端点
- **删除**: `DELETE /api/Management/DeleteConsultationOrder/{orderNumber}`
- **批量删除**: `POST /api/Management/BatchDeleteConsultationOrder`

## 使用示例

### 1. 单个删除
```typescript
// 前端调用
await consultationApi.deleteConsultationOrder(orderNumber)
```

### 2. 批量删除
```typescript
// 前端调用
await consultationApi.batchDeleteConsultationOrders(['order1', 'order2'])
```

### 3. 数据恢复（管理功能）
```csharp
// 后端恢复逻辑（可扩展）
order.IsDeleted = false;
order.DeletedTime = null;
order.DeletedBy = null;
```

## 总结

✅ **已完成功能**:
1. 软删除实体字段扩展
2. AutoMapper配置和映射
3. 删除命令和处理器实现
4. 查询逻辑修改（过滤已删除数据）
5. 前端条件显示和确认删除
6. 业务规则验证

✅ **技术特点**:
1. 使用AutoMapper实现类型安全的映射
2. 软删除确保数据安全和可恢复性
3. 完整的审计追踪（时间、操作人、原因）
4. 前端友好的交互体验

问诊单软删除功能现在已完全实现，提供了安全、可追踪的删除机制，并且只有"已取消"状态的订单才会显示删除按钮！
