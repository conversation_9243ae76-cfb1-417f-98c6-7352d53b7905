<template>
    <div class="consultation-detail">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>问诊单详情</h1>
        </div>

        <div v-loading="loading" class="detail-container">
            <!-- 问诊信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <ChatDotRound />
                    </el-icon>
                    <span>问诊信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>问诊方式：</label>
                        <span>{{ orderDetail.consultationInfo.consultationMethod }}</span>
                    </div>
                    <div class="info-item">
                        <label>问诊来源：</label>
                        <span>{{ orderDetail.consultationInfo.consultationSource }}</span>
                    </div>
                    <div class="info-item">
                        <label>预约时间：</label>
                        <span>{{ formatDateTime(orderDetail.consultationInfo.appointmentTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>接诊时间：</label>
                        <span>{{ formatDateTime(orderDetail.consultationInfo.consultationStartTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>结束时间：</label>
                        <span>{{ formatDateTime(orderDetail.consultationInfo.consultationEndTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>通话时长：</label>
                        <span>{{ orderDetail.consultationInfo.callDurationMinutes || 0 }}分钟</span>
                    </div>
                    <div class="info-item">
                        <label>医生：</label>
                        <span>{{ orderDetail.doctorInfo.doctorName }}</span>
                    </div>
                    <div class="info-item">
                        <label>科室：</label>
                        <span>{{ orderDetail.doctorInfo.department }}</span>
                    </div>
                    <div class="info-item">
                        <label>问诊费：</label>
                        <span class="price">¥{{ orderDetail.orderInfo.consultationFee }}</span>
                    </div>
                </div>
            </div>

            <!-- 订单信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <Document />
                    </el-icon>
                    <span>订单信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>订单编号：</label>
                        <span>{{ orderDetail.orderInfo.orderNumber }}</span>
                    </div>
                    <div class="info-item">
                        <label>订单类型：</label>
                        <span>{{ orderDetail.orderInfo.orderType }}</span>
                    </div>
                    <div class="info-item">
                        <label>订单状态：</label>
                        <el-tag :type="getConsultationStatusType(orderDetail.orderInfo.orderStatus)">
                            {{ orderDetail.orderInfo.orderStatus }}
                        </el-tag>
                    </div>
                    <div class="info-item">
                        <label>总金额：</label>
                        <span class="price">¥{{ orderDetail.orderInfo.totalAmount }}</span>
                    </div>
                    <div class="info-item">
                        <label>优惠金额：</label>
                        <span class="price">¥{{ orderDetail.orderInfo.couponAmount || 0 }}</span>
                    </div>
                    <div class="info-item">
                        <label>实付金额：</label>
                        <span class="price highlight">¥{{ orderDetail.orderInfo.actualPayment }}</span>
                    </div>
                    <div class="info-item">
                        <label>支付方式：</label>
                        <span>{{ orderDetail.orderInfo.paymentMethod || '未支付' }}</span>
                    </div>
                    <div class="info-item">
                        <label>支付时间：</label>
                        <span>{{ formatDateTime(orderDetail.orderInfo.paymentTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>提交时间：</label>
                        <span>{{ formatDateTime(orderDetail.orderInfo.submitTime) }}</span>
                    </div>
                    <div class="info-item">
                        <label>用户名：</label>
                        <span>{{ orderDetail.orderInfo.userName }}</span>
                    </div>
                    <div class="info-item">
                        <label>备注：</label>
                        <span>{{ orderDetail.orderInfo.remarks || '无' }}</span>
                    </div>
                </div>
            </div>

            <!-- 患者信息 -->
            <div class="info-section">
                <div class="section-header">
                    <el-icon>
                        <User />
                    </el-icon>
                    <span>患者信息</span>
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <label>患者姓名：</label>
                        <span>{{ orderDetail.patientInfo.patientName }}</span>
                    </div>
                    <div class="info-item">
                        <label>性别：</label>
                        <span>{{ orderDetail.patientInfo.patientGender }}</span>
                    </div>
                    <div class="info-item">
                        <label>年龄：</label>
                        <span>{{ orderDetail.patientInfo.patientAge }}岁</span>
                    </div>
                    <div class="info-item">
                        <label>联系电话：</label>
                        <span>{{ orderDetail.patientInfo.patientPhone }}</span>
                    </div>
                    <div class="info-item">
                        <label>患者评分：</label>
                        <span>{{ orderDetail.patientInfo.patientRating ? orderDetail.patientInfo.patientRating + '星' :
                            '未评分' }}</span>
                    </div>
                    <div class="info-item">
                        <label>患者评价：</label>
                        <span>{{ orderDetail.patientInfo.patientReview || '无' }}</span>
                    </div>
                    <div class="info-item">
                        <label>症状描述：</label>
                        <span>{{ orderDetail.consultationInfo.symptomDescription || '无' }}</span>
                    </div>
                    <div class="info-item">
                        <label>就诊情况：</label>
                        <span>{{ orderDetail.consultationInfo.medicalHistory || '无' }}</span>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
                <el-button type="primary" @click="goBack">返回列表</el-button>
                <el-button @click="refreshData">刷新数据</el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ChatDotRound, Document, User } from '@element-plus/icons-vue'
import { formatDateTime } from '@/utils/format'
import { consultationApi } from '@/api/order'
import type { ConsultationOrderDetailDto } from '@/types/order'

const router = useRouter()
const route = useRoute()

// 响应式数据
const loading = ref(false)
const orderDetail = reactive<ConsultationOrderDetailDto>({
    consultationInfo: {
        consultationMethod: '',
        consultationSource: '',
        appointmentTime: '',
        consultationStartTime: '',
        consultationEndTime: '',
        callDurationMinutes: 0,
        symptomDescription: '',
        medicalHistory: '',
        hasConsultationRecord: false
    },
    orderInfo: {
        orderNumber: '',
        orderType: '',
        orderStatus: '',
        totalAmount: 0,
        couponAmount: 0,
        actualPayment: 0,
        consultationFee: 0,
        createTime: '',
        paymentMethod: '',
        paymentTime: '',
        submitTime: '',
        userName: '',
        remarks: ''
    },
    patientInfo: {
        patientName: '',
        patientGender: '',
        patientAge: 0,
        patientPhone: '',
        patientRating: 0,
        patientReview: ''
    },
    doctorInfo: {
        doctorName: '',
        department: ''
    }
})

// 获取订单详情
const getOrderDetail = async () => {
    const orderNumber = route.query.orderNumber as string
    if (!orderNumber) {
        ElMessage.error('订单编号不能为空')
        return
    }

    loading.value = true
    try {
        console.log('获取问诊单详情，订单号:', orderNumber)

        const response = await consultationApi.getConsultationOrderDetail(orderNumber)
        console.log('API响应:', response.data)

        if (response.data.code === 200) {
            // 数据反填
            Object.assign(orderDetail, response.data.data)
            ElMessage.success('订单详情加载成功')
        } else {
            console.error('API返回失败:', response.data)
            ElMessage.error(response.data?.message || '获取订单详情失败')
        }
    } catch (error: any) {
        console.error('获取问诊单详情失败:', error)
        ElMessage.error('获取问诊单详情失败')
    } finally {
        loading.value = false
    }
}

// 获取问诊单状态类型
const getConsultationStatusType = (status: string) => {
    const statusTypeMap: Record<string, string> = {
        '待支付': 'warning',
        '进行中': 'primary',
        '已完成': 'success',
        '已取消': 'info',
        '已退诊': 'danger'
    }
    return statusTypeMap[status] || 'info'
}



// 返回上一页
const goBack = () => {
    router.back()
}

// 刷新数据
const refreshData = () => {
    getOrderDetail()
}

// 组件挂载时获取数据
onMounted(() => {
    getOrderDetail()
})
</script>

<style scoped>
.consultation-detail {
    padding: 20px;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.page-header h1 {
    margin: 0 0 20px 0;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

.detail-container {
    max-width: 1200px;
    margin: 0 auto;
}

.info-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
}

.section-header .el-icon {
    margin-right: 8px;
    color: #409eff;
    font-size: 20px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    align-items: center;
    padding: 10px 0;
}

.info-item label {
    font-weight: 600;
    color: #666;
    min-width: 100px;
    margin-right: 10px;
}

.info-item span {
    color: #333;
    flex: 1;
}

.price {
    font-weight: 600;
    color: #e6a23c;
}

.price.highlight {
    color: #f56c6c;
    font-size: 16px;
}

.action-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.action-section .el-button {
    margin: 0 10px;
}

@media (max-width: 768px) {
    .info-grid {
        grid-template-columns: 1fr;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .info-item label {
        margin-bottom: 5px;
    }
}
</style>