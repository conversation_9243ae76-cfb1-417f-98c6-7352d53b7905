{"Version": 1, "WorkspaceRootPath": "D:\\实训一\\项目\\工单\\DeepSeek\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{B8F3D8A1-2C4E-4F5A-9B7C-1E8D9F6A2B3C}|DeepSeek\\DeepSeek.csproj|d:\\实训一\\项目\\工单\\deepseek\\deepseek\\embeddingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B8F3D8A1-2C4E-4F5A-9B7C-1E8D9F6A2B3C}|DeepSeek\\DeepSeek.csproj|solutionrelative:deepseek\\embeddingservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B8F3D8A1-2C4E-4F5A-9B7C-1E8D9F6A2B3C}|DeepSeek\\DeepSeek.csproj|d:\\实训一\\项目\\工单\\deepseek\\deepseek\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{B8F3D8A1-2C4E-4F5A-9B7C-1E8D9F6A2B3C}|DeepSeek\\DeepSeek.csproj|solutionrelative:deepseek\\appsettings.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{B8F3D8A1-2C4E-4F5A-9B7C-1E8D9F6A2B3C}|DeepSeek\\DeepSeek.csproj|d:\\实训一\\项目\\工单\\deepseek\\deepseek\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B8F3D8A1-2C4E-4F5A-9B7C-1E8D9F6A2B3C}|DeepSeek\\DeepSeek.csproj|solutionrelative:deepseek\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 5, "Children": [{"$type": "Bookmark", "Name": "ST:128:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:129:0:{116d2292-e37d-41cd-a077-ebacac4c8cc4}"}, {"$type": "Bookmark", "Name": "ST:0:0:{1c4feeaa-4718-4aa9-859d-94ce25d182ba}"}, {"$type": "Bookmark", "Name": "ST:0:0:{cce594b6-0c39-4442-ba28-10c64ac7e89f}"}, {"$type": "Bookmark", "Name": "ST:0:0:{3ae79031-e1bc-11d0-8f78-00a0c9110057}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "EmbeddingService.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\EmbeddingService.cs", "RelativeDocumentMoniker": "DeepSeek\\EmbeddingService.cs", "ToolTip": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\EmbeddingService.cs", "RelativeToolTip": "DeepSeek\\EmbeddingService.cs", "ViewState": "AQIAAAAAAAAAAAAAAAAAAFgAAAAcAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-01T06:55:21.827Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "Program.cs", "DocumentMoniker": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\Program.cs", "RelativeDocumentMoniker": "DeepSeek\\Program.cs", "ToolTip": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\Program.cs", "RelativeToolTip": "DeepSeek\\Program.cs", "ViewState": "AQIAANIBAAAAAAAAAAAwwO4BAAAAAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-31T01:48:29.355Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "appsettings.json", "DocumentMoniker": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\appsettings.json", "RelativeDocumentMoniker": "DeepSeek\\appsettings.json", "ToolTip": "D:\\实训一\\项目\\工单\\DeepSeek\\DeepSeek\\appsettings.json", "RelativeToolTip": "DeepSeek\\appsettings.json", "ViewState": "AQIAAAAAAAAAAAAAAAAAAAQAAAAcAAAA", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-31T01:28:13.757Z", "EditorCaption": ""}]}]}]}