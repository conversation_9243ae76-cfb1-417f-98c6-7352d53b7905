# 工单管理系统前端

## 项目简介

这是一个基于 Vue 3 + TypeScript + Element Plus 的工单管理系统前端项目，主要功能包括：

- 挂号订单管理（列表展示、筛选、分页）
- 订单详情查看（数据反填）
- 响应式设计和现代化UI

## 技术栈

- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全的JavaScript超集
- **Element Plus** - Vue 3的UI组件库
- **Vue Router** - Vue.js官方路由管理器
- **Axios** - HTTP客户端
- **Moment.js** - 日期时间处理库
- **Vite** - 构建工具

## 项目结构

```
src/
├── api/           # API服务
│   └── order.ts   # 订单相关API
├── components/    # 公共组件
├── config/        # 配置文件
│   └── index.ts   # 全局配置
├── router/        # 路由配置
│   └── index.ts   # 路由定义
├── types/         # TypeScript类型定义
│   └── order.ts   # 订单相关类型
├── utils/         # 工具函数
│   └── format.ts  # 格式化工具
├── views/         # 页面组件
│   └── Order/     # 订单相关页面
│       ├── RegistrationShow.vue  # 挂号订单列表
│       └── RegistrationFan.vue   # 订单详情
└── main.ts        # 应用入口
```

## 功能特性

### 1. 挂号订单管理页面 (RegistrationShow.vue)

- **筛选功能**：支持按订单号/用户名搜索，按时间范围筛选
- **状态标签页**：全部、待支付、已完成、已取消、已退款
- **数据表格**：展示订单编号、提交时间、用户名、医生、科室、挂号费、订单状态
- **分页功能**：支持页码跳转、每页条数调整
- **查看详情**：点击"查看订单"跳转到详情页面

### 2. 订单详情页面 (RegistrationFan.vue)

- **挂号信息**：就诊时间、医院、科室、医生、费用等
- **订单信息**：订单编号、状态、金额明细
- **就诊人信息**：患者姓名、性别、年龄、联系方式
- **数据反填**：自动填充订单详情数据
- **返回功能**：支持返回列表页面

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

在项目根目录创建 `.env` 文件：

```env
# API基础地址
VITE_API_BASE_URL=http://localhost:5000

# 应用标题
VITE_APP_TITLE=工单管理系统
```

### 3. 启动开发服务器

```bash
npm run dev
```

### 4. 构建生产版本

```bash
npm run build
```

## API接口

### 获取挂号订单列表

```
GET /api/Management/GetRegistrationOrder
```

**请求参数：**
- `pageIndex`: 页码（默认1）
- `pageSize`: 每页条数（默认10）
- `orderNumber`: 订单号（可选）
- `begin`: 开始时间（可选）
- `end`: 结束时间（可选）

### 获取订单详情

```
GET /api/Management/GetOrderDetail/{orderNumber}
```

**路径参数：**
- `orderNumber`: 订单编号

## 使用说明

### 1. 查看订单列表

1. 访问挂号订单管理页面
2. 使用筛选功能查找特定订单
3. 通过状态标签页筛选不同状态的订单
4. 使用分页功能浏览更多订单

### 2. 查看订单详情

1. 在订单列表中点击"查看订单"按钮
2. 系统自动跳转到订单详情页面
3. 查看完整的订单信息
4. 点击"返回"按钮回到列表页面

### 3. 数据筛选

- **关键词搜索**：输入订单号或用户名进行模糊搜索
- **时间筛选**：选择开始时间和结束时间进行范围筛选
- **状态筛选**：点击状态标签页筛选特定状态的订单

## 开发指南

### 1. 添加新的API接口

在 `src/api/order.ts` 中添加新的API方法：

```typescript
export const orderApi = {
  // 现有方法...
  
  // 新增方法
  newApiMethod: (params: NewParams) => {
    return axios.get<ApiResponse<NewResponse>>('/api/new-endpoint', { params })
  }
}
```

### 2. 添加新的类型定义

在 `src/types/order.ts` 中添加新的接口定义：

```typescript
export interface NewInterface {
  id: string
  name: string
  // 其他属性...
}
```

### 3. 添加新的工具函数

在 `src/utils/format.ts` 中添加新的格式化函数：

```typescript
export const newFormatFunction = (value: any): string => {
  // 格式化逻辑
  return formattedValue
}
```

## 注意事项

1. **API地址配置**：确保后端API服务正在运行，并正确配置API地址
2. **跨域问题**：如果遇到跨域问题，需要在后端配置CORS
3. **数据格式**：确保后端返回的数据格式与前端类型定义一致
4. **错误处理**：系统已包含基本的错误处理机制，可根据需要扩展

## 更新日志

### v1.0.0
- 初始版本发布
- 实现挂号订单管理功能
- 实现订单详情查看功能
- 支持筛选、分页、数据反填
