﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using WorkOrder.API.Read.Application.command;
using WorkOrder.Domain;
using WorkOrder.ErrorCode;

namespace WorkOrder.API.Read.Controllers
{
    [Route("api/[controller]/[action]")]
    [ApiController]
    public class ManagementController : ControllerBase
    {
        private readonly IMediator mediator;

        public ManagementController(IMediator mediator)
        {
            this.mediator = mediator;
        }
        /// <summary>
        /// 获取挂号列表
        /// </summary>
        /// <param name="request">命令</param>
        /// <returns>返回任务</returns>
        [HttpGet]
        public Task<APIResult<APIPageing<RegistrationOrder>>> GetRegistrationOrder([FromQuery] RegistrationOrderQuerycommand request)
        {
            return mediator.Send(request);
        }

        /// <summary>
        /// 获取问诊单列表
        /// </summary>
        /// <param name="request">查询命令</param>
        /// <returns>返回问诊单分页数据</returns>
        [HttpGet]
        public Task<APIResult<APIPageing<ConsultationOrder>>> GetConsultationOrder([FromQuery] ConsultationOrderQueryCommand request)
        {
            return mediator.Send(request);
        }

        /// <summary>
        /// 获取问诊单详情（用于反填功能）
        /// </summary>
        /// <param name="orderNumber">订单编号</param>
        /// <returns>问诊单详情信息</returns>
        [HttpGet("{orderNumber}")]
        public Task<APIResult<ConsultationOrderDetailDto>> GetConsultationOrderDetail(string orderNumber)
        {
            var command = new GetConsultationOrderDetailCommand { OrderNumber = orderNumber };
            return mediator.Send(command);
        }

        /// <summary>
        /// 获取挂号单详情（用于反填功能）
        /// </summary>
        /// <param name="orderNumber">订单编号</param>
        /// <returns>订单详情信息</returns>
        [HttpGet("{orderNumber}")]
        public Task<APIResult<OrderDetailDto>> GetOrderDetail(string orderNumber)
        {
            var command = new GetOrderDetailCommand { OrderNumber = orderNumber };
            return mediator.Send(command);
        }

    }
}
