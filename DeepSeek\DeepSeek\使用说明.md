# DeepSeek RAG 系统使用说明

## 🎉 恭喜！RAG系统已成功实现

您的RAG（检索增强生成）系统已经完全实现，包含以下核心功能：

### ✅ 已实现的功能

1. **文档处理模块** (`DocumentProcessor`)
   - 文档加载和预处理
   - 智能文档分块（支持重叠）
   - 向量嵌入生成

2. **向量存储** (`VectorStore`)
   - 内存向量数据库
   - 余弦相似度计算
   - 高效相似度搜索

3. **RAG检索器** (`RAGRetriever`)
   - 查询向量化
   - 相关文档检索
   - 上下文格式化

4. **DeepSeek客户端** (`DeepSeekClient`)
   - DeepSeek API 集成
   - 智能回答生成
   - 错误处理

5. **RAG生成器** (`RAGGenerator`)
   - 端到端RAG流程
   - 检索和生成协调

6. **嵌入服务** (`SimpleEmbeddingService`)
   - 文本向量化
   - 语义相似度计算

## 🚀 如何使用

### 1. 配置API密钥

在使用前，请设置您的DeepSeek API密钥：

**方法一：环境变量**
```bash
# Windows
set DEEPSEEK_API_KEY=your_actual_deepseek_api_key

# Linux/Mac
export DEEPSEEK_API_KEY=your_actual_deepseek_api_key
```

**方法二：修改代码**
在 `Program.cs` 中找到这一行：
```csharp
var apiKey = Environment.GetEnvironmentVariable("DEEPSEEK_API_KEY") ?? 
            "your-deepseek-api-key-here";
```
将 `"your-deepseek-api-key-here"` 替换为您的实际API密钥。

### 2. 运行系统

```bash
cd DeepSeek
dotnet run
```

### 3. 添加文档

系统会自动加载 `sample_documents/` 目录下的文档文件：
- `ai_introduction.txt` - 人工智能技术概述
- `rag_system.txt` - RAG系统详解

您可以在该目录下添加更多 `.txt` 文件。

### 4. 开始问答

系统启动后，您可以输入问题，例如：
- "什么是人工智能？"
- "RAG系统是如何工作的？"
- "机器学习有哪些类型？"

输入 `quit` 退出系统。

## 🔧 系统架构

```
用户问题 → 向量化 → 相似度检索 → 上下文构建 → DeepSeek生成 → 智能回答
```

### 核心组件关系

```
DocumentProcessor ──→ VectorStore
       ↓                  ↓
   文档分块           向量存储
       ↓                  ↓
RAGRetriever ←──────── 相似度搜索
       ↓
   检索结果
       ↓
RAGGenerator ──→ DeepSeekClient ──→ 最终回答
```

## ⚙️ 配置参数

### 文档处理参数
- `chunkSize`: 文档块大小（默认500字符）
- `overlap`: 块重叠大小（默认50字符）

### 检索参数
- `topK`: 返回文档数量（默认3）
- `threshold`: 相似度阈值（默认0.1）

### 生成参数
- `temperature`: 生成随机性（默认0.7）
- `max_tokens`: 最大生成长度（默认2000）

## 🛠️ 自定义和扩展

### 1. 使用真实的嵌入服务

当前使用的是演示用的简单嵌入服务。要使用真实的嵌入服务：

**OpenAI嵌入服务：**
```bash
dotnet add package Microsoft.SemanticKernel.Connectors.OpenAI
```

**Azure OpenAI嵌入服务：**
参考 `EmbeddingService.cs` 中的配置示例。

### 2. 添加更多文档格式

扩展 `DocumentProcessor` 以支持：
- PDF文件
- Word文档
- HTML页面
- Markdown文件

### 3. 持久化存储

将 `VectorStore` 扩展为：
- 文件存储
- 数据库存储（如PostgreSQL + pgvector）
- 云向量数据库（如Pinecone、Weaviate）

### 4. 改进检索质量

- 实现混合检索（关键词+语义）
- 添加重排序模型
- 实现查询扩展

## 🐛 故障排除

### 常见问题

**Q: API调用失败**
A: 检查DeepSeek API密钥是否正确配置

**Q: 找不到相关文档**
A: 降低相似度阈值或检查文档内容是否相关

**Q: 系统响应慢**
A: 考虑使用更快的嵌入服务或优化向量索引

**Q: 内存使用过高**
A: 减少文档数量或实现分批处理

### 调试技巧

1. 查看日志输出了解系统运行状态
2. 调整相似度阈值观察检索效果
3. 检查文档分块是否合理
4. 验证向量嵌入是否正确生成

## 📈 性能优化建议

1. **向量索引优化**：使用FAISS等高效向量索引
2. **缓存机制**：缓存常见查询的结果
3. **并行处理**：并行化文档处理和向量生成
4. **模型优化**：使用量化模型减少内存占用

## 🔮 未来扩展方向

1. **多模态支持**：图像、音频等多种数据类型
2. **实时更新**：支持文档的实时添加和更新
3. **个性化**：根据用户偏好定制检索和生成
4. **分布式部署**：支持大规模分布式部署

## 📞 技术支持

如果您在使用过程中遇到问题：

1. 查看控制台日志输出
2. 检查配置文件设置
3. 参考README.md文档
4. 查看示例代码注释

---

**恭喜您成功实现了一个完整的RAG系统！** 🎊

这个系统展示了现代AI应用的核心技术栈，包括文档处理、向量检索、语言模型集成等关键组件。您可以基于这个基础继续扩展和优化，构建更强大的智能问答系统。
