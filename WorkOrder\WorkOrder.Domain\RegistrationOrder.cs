﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace WorkOrder.Domain
{
    /// <summary>
    /// 挂号单实体类
    /// </summary>
    [Table("RegistrationOrder")]
    public class RegistrationOrder
    {
        /// <summary>
        /// 挂号订单编号（主键）- 格式：REG + 年月日 + 6位序号，如：REG20240101000001
        /// </summary>
        [Key]
        public  string OrderNumber { get; set; }

        /// <summary>
        /// 挂号单号
        /// </summary>
        public  string RegistrationNumber { get; set; }

        /// <summary>
        /// 就诊时间
        /// </summary>
        public DateTime AppointmentDateTime { get; set; }

        /// <summary>
        /// 就诊医院
        /// </summary>
        public  string Hospital { get; set; }

        /// <summary>
        /// 科室
        /// </summary>
        public  string Department { get; set; }

        /// <summary>
        /// 医生姓名
        /// </summary>

        public  string DoctorName { get; set; }

        /// <summary>
        /// 医生职称
        /// </summary>
        public string? DoctorTitle { get; set; }

        /// <summary>
        /// 医事服务费
        /// </summary>

        public decimal MedicalServiceFee { get; set; }

        /// <summary>
        /// 应付金额
        /// </summary>
   
        public decimal TotalAmount { get; set; }

        /// <summary>
        /// 优惠券金额
        /// </summary>
        public decimal? CouponAmount { get; set; }

        /// <summary>
        /// 实际支付金额
        /// </summary>
     
        public decimal ActualPayment { get; set; }

        /// <summary>
        /// 订单状态（待支付、已完成、已取消、已退款）
        /// </summary>
        public  string OrderStatus { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>

        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 支付方式
        /// </summary>
  
        public string? PaymentMethod { get; set; }

        /// <summary>
        /// 支付时间
        /// </summary>
        public DateTime? PaymentTime { get; set; }

        /// <summary>
        /// 退款金额
        /// </summary>
        public decimal? RefundAmount { get; set; }

        /// <summary>
        /// 退款时间
        /// </summary>
        public DateTime? RefundTime { get; set; }

        /// <summary>
        /// 患者姓名
        /// </summary>
        public  string PatientName { get; set; }

        /// <summary>
        /// 患者性别
        /// </summary>
        public  string PatientGender { get; set; }

        /// <summary>
        /// 患者年龄
        /// </summary>
        public int PatientAge { get; set; }

        /// <summary>
        /// 患者联系电话
        /// </summary>

        public  string PatientPhone { get; set; }

        /// <summary>
        /// 提交时间（用于管理列表显示）
        /// </summary>
 
        public DateTime SubmitTime { get; set; }

        /// <summary>
        /// 用户名（提交订单的用户）
        /// </summary>

        public string UserName { get; set; }
    }
}
