# 状态标签数据显示修复说明

## 问题描述
点击状态标签时显示的数据不正确，例如：
- 点击"进行中"标签，显示的却是"待支付"的数据
- 点击"已完成"标签，显示的却是其他状态的数据
- 状态筛选功能完全失效

## 问题根本原因

### 1. 状态映射逻辑错误
**原始代码问题**:
```typescript
// 错误的状态处理逻辑
orderStatus: activeStatus.value === 'all' ? (filterForm.orderStatus || undefined) : getStatusMapping(activeStatus.value)
```

**问题分析**:
- `activeStatus.value` 是英文状态名（如 'inprogress'）
- `getStatusMapping()` 将英文转换为中文（如 '进行中'）
- 但是这个函数的逻辑混乱，没有正确处理"全部"状态的情况

### 2. API参数传递错误
- 后端API期望接收中文状态名作为查询参数
- 前端传递的参数格式不正确
- 状态映射函数没有考虑到"全部"状态的特殊处理

## 修复方案

### 1. 重新设计状态处理函数

**新增专用函数** `getOrderStatusForAPI()`:
```typescript
// 获取API查询用的订单状态
const getOrderStatusForAPI = () => {
    // 如果是"全部"标签，优先使用筛选表单中的状态，否则不传状态参数
    if (activeStatus.value === 'all') {
        return filterForm.orderStatus || undefined
    }
    
    // 根据当前选中的状态标签，返回对应的中文状态名
    const statusMap: Record<string, string> = {
        'pending': '待支付',
        'completed': '已完成',
        'cancelled': '已取消',
        'inprogress': '进行中',
        'refunded': '已退诊'
    }
    
    const chineseStatus = statusMap[activeStatus.value]
    console.log(`状态标签: ${activeStatus.value} -> API参数: ${chineseStatus}`)
    return chineseStatus
}
```

### 2. 修改API参数构建逻辑

**修改前**:
```typescript
orderStatus: activeStatus.value === 'all' ? (filterForm.orderStatus || undefined) : getStatusMapping(activeStatus.value)
```

**修改后**:
```typescript
orderStatus: getOrderStatusForAPI()
```

## 修复后的工作流程

### 1. 用户点击状态标签
- 用户点击"进行中"标签
- `activeStatus.value` 变为 `'inprogress'`

### 2. 状态处理逻辑
- 调用 `getOrderStatusForAPI()` 函数
- 函数检查 `activeStatus.value` 不是 `'all'`
- 通过状态映射表将 `'inprogress'` 转换为 `'进行中'`
- 返回中文状态名 `'进行中'`

### 3. API请求参数
- 构建查询参数：`{ orderStatus: '进行中' }`
- 发送请求到后端API
- 后端根据中文状态名筛选数据

### 4. 数据显示
- 接收到筛选后的数据
- 只显示状态为"进行中"的问诊单
- 用户看到正确的筛选结果

## 状态映射对照表

| 前端标签页name | 显示文本 | API参数值 | 后端筛选结果 |
|---------------|----------|-----------|-------------|
| all | 全部 | undefined | 所有状态 |
| pending | 待支付 | '待支付' | 只显示待支付订单 |
| inprogress | 进行中 | '进行中' | 只显示进行中订单 |
| completed | 已完成 | '已完成' | 只显示已完成订单 |
| cancelled | 已取消 | '已取消' | 只显示已取消订单 |
| refunded | 已退诊 | '已退诊' | 只显示已退诊订单 |

## 特殊情况处理

### 1. "全部"标签的处理
- 当选择"全部"标签时，不传递 `orderStatus` 参数
- 如果筛选表单中有选择状态，则使用表单中的状态
- 否则返回 `undefined`，让后端返回所有状态的数据

### 2. 调试支持
- 添加控制台日志：`console.log(\`状态标签: ${activeStatus.value} -> API参数: ${chineseStatus}\`)`
- 便于开发时调试状态映射是否正确

### 3. 错误处理
- 如果状态映射表中没有对应的状态，返回原始值
- 确保不会因为未知状态导致程序崩溃

## 测试验证

### 1. 功能测试步骤
1. 打开问诊单管理页面
2. 点击"待支付"标签 → 应该只显示待支付状态的订单
3. 点击"进行中"标签 → 应该只显示进行中状态的订单
4. 点击"已完成"标签 → 应该只显示已完成状态的订单
5. 点击"已取消"标签 → 应该只显示已取消状态的订单
6. 点击"已退诊"标签 → 应该只显示已退诊状态的订单
7. 点击"全部"标签 → 应该显示所有状态的订单

### 2. 验证方法
- 检查表格中显示的订单状态是否与选中的标签一致
- 查看浏览器控制台的日志，确认API参数正确
- 检查网络请求，确认发送给后端的参数格式正确

## 代码改动总结

### 修改的文件
- `workorderui\src\views\Order\ConsultationShow.vue`

### 主要改动
1. **新增函数**: `getOrderStatusForAPI()` - 专门处理状态映射
2. **修改API参数构建**: 使用新函数替代原有的复杂逻辑
3. **添加调试日志**: 便于开发调试
4. **删除冗余代码**: 移除不再使用的 `getStatusMapping` 函数

### 改动行数
- 新增：17行（新的状态处理函数）
- 修改：1行（API参数构建）
- 删除：0行（保留原函数以防其他地方使用）

## 总结

通过重新设计状态处理逻辑，现在：

✅ **问题已解决**:
- 点击"进行中"标签，正确显示进行中状态的订单
- 点击"待支付"标签，正确显示待支付状态的订单
- 所有状态标签都能正确筛选对应状态的数据

✅ **功能增强**:
- 更清晰的状态映射逻辑
- 更好的调试支持
- 更健壮的错误处理

✅ **用户体验**:
- 状态筛选功能完全正常
- 点击标签立即看到正确的筛选结果
- 与其他筛选条件可以正常组合使用

现在用户可以通过点击不同的状态标签来准确筛选对应状态的问诊单，解决了数据显示不正确的问题！
