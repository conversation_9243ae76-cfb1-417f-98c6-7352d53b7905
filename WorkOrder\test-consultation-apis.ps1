# 测试问诊单API的PowerShell脚本

$baseUrl = "http://localhost:5202"

Write-Host "=== 测试问诊单API ===" -ForegroundColor Green

# 1. 测试问诊单列表查询
Write-Host "`n1. 测试问诊单列表查询..." -ForegroundColor Yellow
try {
    $response = Invoke-RestMethod -Uri "$baseUrl/api/Management/GetConsultationOrder?PageIndex=1&PageSize=5" -Method GET
    Write-Host "✓ 问诊单列表查询成功" -ForegroundColor Green
    Write-Host "总数量: $($response.data.totalCount)" -ForegroundColor Cyan
    Write-Host "页数: $($response.data.pageCount)" -ForegroundColor Cyan
    Write-Host "当前页数据量: $($response.data.pageData.Count)" -ForegroundColor Cyan
    
    if ($response.data.pageData.Count -gt 0) {
        $firstOrder = $response.data.pageData[0]
        Write-Host "第一个订单号: $($firstOrder.orderNumber)" -ForegroundColor Cyan
        $testOrderNumber = $firstOrder.orderNumber
    }
} catch {
    Write-Host "✗ 问诊单列表查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 2. 测试问诊单详情查询（反填功能）
if ($testOrderNumber) {
    Write-Host "`n2. 测试问诊单详情查询（反填功能）..." -ForegroundColor Yellow
    try {
        $detailResponse = Invoke-RestMethod -Uri "$baseUrl/api/Management/consultation/$testOrderNumber" -Method GET
        Write-Host "✓ 问诊单详情查询成功" -ForegroundColor Green
        Write-Host "订单号: $($detailResponse.data.orderInfo.orderNumber)" -ForegroundColor Cyan
        Write-Host "患者姓名: $($detailResponse.data.patientInfo.patientName)" -ForegroundColor Cyan
        Write-Host "医生姓名: $($detailResponse.data.doctorInfo.doctorName)" -ForegroundColor Cyan
        Write-Host "问诊方式: $($detailResponse.data.consultationInfo.consultationMethod)" -ForegroundColor Cyan
        Write-Host "订单状态: $($detailResponse.data.orderInfo.orderStatus)" -ForegroundColor Cyan
    } catch {
        Write-Host "✗ 问诊单详情查询失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 3. 测试带筛选条件的问诊单查询
Write-Host "`n3. 测试带筛选条件的问诊单查询..." -ForegroundColor Yellow
try {
    $filterResponse = Invoke-RestMethod -Uri "$baseUrl/api/Management/GetConsultationOrder?PageIndex=1&PageSize=3&OrderStatus=已完成" -Method GET
    Write-Host "✓ 筛选查询成功" -ForegroundColor Green
    Write-Host "筛选结果数量: $($filterResponse.data.pageData.Count)" -ForegroundColor Cyan
} catch {
    Write-Host "✗ 筛选查询失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 4. 测试通用订单详情查询（兼容性测试）
if ($testOrderNumber) {
    Write-Host "`n4. 测试通用订单详情查询（兼容性测试）..." -ForegroundColor Yellow
    try {
        $generalDetailResponse = Invoke-RestMethod -Uri "$baseUrl/api/Management/$testOrderNumber" -Method GET
        Write-Host "✓ 通用订单详情查询成功" -ForegroundColor Green
        Write-Host "订单号: $($generalDetailResponse.data.orderInfo.orderNumber)" -ForegroundColor Cyan
        Write-Host "患者姓名: $($generalDetailResponse.data.patientInfo.patientName)" -ForegroundColor Cyan
    } catch {
        Write-Host "✗ 通用订单详情查询失败: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
