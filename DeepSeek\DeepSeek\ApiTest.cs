using System.Text;
using System.Text.Json;

namespace DeepSeekRAG
{
    public class ApiConnectionTest
    {
        private readonly HttpClient _httpClient;
        private readonly string _apiKey;

        public ApiConnectionTest()
        {
            _httpClient = new HttpClient();
            _apiKey = "sk-639b4fc707e5441098d8f084eb5ef0aa";
        }

        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                Console.WriteLine("🔍 开始测试DeepSeek API连接...");
                Console.WriteLine($"API密钥: {_apiKey.Substring(0, 10)}...");

                var requestBody = new
                {
                    model = "deepseek-chat",
                    messages = new[]
                    {
                        new { role = "user", content = "Hello, this is a test message." }
                    },
                    max_tokens = 50
                };

                var json = JsonSerializer.Serialize(requestBody);
                var content = new StringContent(json, Encoding.UTF8, "application/json");

                _httpClient.DefaultRequestHeaders.Clear();
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");

                Console.WriteLine("📡 发送测试请求到DeepSeek API...");
                
                var response = await _httpClient.PostAsync("https://api.deepseek.com/v1/chat/completions", content);

                Console.WriteLine($"📊 响应状态码: {response.StatusCode}");

                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"📄 响应内容: {responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("✅ API连接成功！");
                    return true;
                }
                else
                {
                    Console.WriteLine("❌ API连接失败！");
                    Console.WriteLine($"错误详情: {responseContent}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"💥 连接异常: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
                return false;
            }
        }

        public async Task TestNetworkConnectivity()
        {
            try
            {
                Console.WriteLine("\n🌐 测试网络连接...");
                
                // 测试基本网络连接
                var pingResponse = await _httpClient.GetAsync("https://www.baidu.com");
                Console.WriteLine($"百度连接: {(pingResponse.IsSuccessStatusCode ? "✅ 成功" : "❌ 失败")}");

                // 测试DeepSeek域名解析
                var deepseekResponse = await _httpClient.GetAsync("https://api.deepseek.com");
                Console.WriteLine($"DeepSeek域名: {(deepseekResponse.StatusCode != System.Net.HttpStatusCode.NotFound ? "✅ 可访问" : "❌ 无法访问")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"网络测试失败: {ex.Message}");
            }
        }

        public void ValidateApiKey()
        {
            Console.WriteLine("\n🔑 验证API密钥格式...");
            
            if (string.IsNullOrEmpty(_apiKey))
            {
                Console.WriteLine("❌ API密钥为空");
                return;
            }

            if (_apiKey.StartsWith("sk-"))
            {
                Console.WriteLine("✅ API密钥格式正确 (以sk-开头)");
            }
            else
            {
                Console.WriteLine("⚠️ API密钥格式可能不正确 (不以sk-开头)");
            }

            Console.WriteLine($"密钥长度: {_apiKey.Length} 字符");
            
            if (_apiKey.Length < 20)
            {
                Console.WriteLine("⚠️ API密钥长度可能不正确 (太短)");
            }
            else
            {
                Console.WriteLine("✅ API密钥长度看起来正常");
            }
        }

        public static async Task RunDiagnostics()
        {
            var tester = new ApiConnectionTest();
            
            Console.WriteLine("=== DeepSeek API 连接诊断工具 ===\n");
            
            // 1. 验证API密钥格式
            tester.ValidateApiKey();
            
            // 2. 测试网络连接
            await tester.TestNetworkConnectivity();
            
            // 3. 测试API连接
            Console.WriteLine("\n🧪 API连接测试:");
            var success = await tester.TestConnectionAsync();
            
            Console.WriteLine("\n" + new string('=', 50));
            if (success)
            {
                Console.WriteLine("🎉 诊断完成: API连接正常！");
            }
            else
            {
                Console.WriteLine("🚨 诊断完成: 发现连接问题，请检查上述错误信息。");
            }
        }
    }
}
